import { prisma } from '../../config/database';
import { logger } from '../../utils/logger';
import { Context } from '../context';
import { AnalyticsService } from '../../services/analytics.service';

/**
 * Helper function to get inquiry statistics
 */
async function getInquiryStats(venueId: string, startDate: Date, endDate: Date) {
  const inquiries = await prisma.inquiry.findMany({
    where: {
      venueId,
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    },
    include: {
      messages: true,
    },
  });

  const total = inquiries.length;
  const newInquiries = inquiries.filter(i => i.status === 'new').length;
  const responded = inquiries.filter(i => i.status === 'responded' || i.status === 'closed').length;

  // Get average response time from analytics service
  const avgResponseTime = await AnalyticsService.getAverageResponseTime(venueId, startDate, endDate);

  // Channel distribution
  const channelCounts = inquiries.reduce((acc, inquiry) => {
    const channel = inquiry.source || 'website';
    acc[channel] = (acc[channel] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const channelDistribution = Object.entries(channelCounts).map(([channel, count]) => ({
    channel,
    count,
    percentage: (count / total) * 100,
  }));

  return {
    total,
    new: newInquiries,
    responded,
    avgResponseTime,
    channelDistribution,
  };
}

/**
 * Helper function to get lead statistics
 */
async function getLeadStats(venueId: string, startDate: Date, endDate: Date) {
  const leads = await prisma.lead.findMany({
    where: {
      venueId,
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    },
  });

  const total = leads.length;
  const newLeads = leads.filter(l => l.stage === 'new').length;
  const active = leads.filter(l => ['contacted', 'qualified', 'proposal'].includes(l.stage || '')).length;
  const won = leads.filter(l => l.stage === 'won').length;
  const lost = leads.filter(l => l.stage === 'lost').length;

  // Calculate average lead value (this would need to be enhanced based on actual booking data)
  const avgValue = leads.length > 0 ? 5000 : 0; // Placeholder - should be calculated from actual booking values

  return {
    total,
    new: newLeads,
    active,
    won,
    lost,
    avgValue,
  };
}

/**
 * Helper function to get conversion statistics
 */
async function getConversionStats(venueId: string, startDate: Date, endDate: Date) {
  // Use the analytics service for accurate conversion calculations
  const conversionRates = await AnalyticsService.calculateConversionRates(venueId, startDate, endDate);

  // Calculate average time to conversion from analytics events
  const conversionEvents = await prisma.analyticsEvent.findMany({
    where: {
      venueId,
      type: {
        name: 'lead-won',
      },
      occurredAt: {
        gte: startDate,
        lte: endDate,
      },
    },
    include: {
      lead: {
        select: {
          createdAt: true,
        },
      },
    },
  });

  let avgTimeToConversion = 0;
  if (conversionEvents.length > 0) {
    const totalDays = conversionEvents.reduce((sum, event) => {
      if (event.lead) {
        const daysDiff = Math.floor(
          (event.occurredAt.getTime() - event.lead.createdAt.getTime()) / (1000 * 60 * 60 * 24)
        );
        return sum + daysDiff;
      }
      return sum;
    }, 0);
    avgTimeToConversion = totalDays / conversionEvents.length;
  }

  return {
    ...conversionRates,
    avgTimeToConversion,
  };
}

/**
 * Helper function to get top sources
 */
async function getTopSources(venueId: string, startDate: Date, endDate: Date) {
  // Use analytics service for more accurate source tracking
  return await AnalyticsService.getTopSources(venueId, startDate, endDate, 10);
}

/**
 * Helper function to get event type distribution
 */
async function getEventTypeDistribution(venueId: string, startDate: Date, endDate: Date) {
  const leads = await prisma.lead.findMany({
    where: {
      venueId,
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    },
  });

  const eventTypeCounts = leads.reduce((acc, lead) => {
    const eventType = lead.eventType || 'wedding';
    acc[eventType] = (acc[eventType] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const total = leads.length;
  return Object.entries(eventTypeCounts).map(([eventType, count]) => ({
    eventType,
    count,
    percentage: total > 0 ? (count / total) * 100 : 0,
  }));
}

/**
 * Helper function to get lead stage distribution
 */
async function getLeadStageDistribution(venueId: string, startDate: Date, endDate: Date) {
  const leads = await prisma.lead.findMany({
    where: {
      venueId,
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    },
  });

  const stageCounts = leads.reduce((acc, lead) => {
    const stage = lead.stage || 'new';
    acc[stage] = (acc[stage] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const total = leads.length;
  return Object.entries(stageCounts).map(([stage, count]) => ({
    stage,
    count,
    percentage: total > 0 ? (count / total) * 100 : 0,
  }));
}

/**
 * Helper function to get user performance
 */
async function getUserPerformance(venueId: string, startDate: Date, endDate: Date) {
  const venueUsers = await prisma.venueUser.findMany({
    where: { venueId },
    include: { user: true },
  });

  const userStats = await Promise.all(
    venueUsers.map(async (venueUser) => {
      const inquiriesHandled = await prisma.inquiry.count({
        where: {
          venueId,
          assignedToId: venueUser.userId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      const leadsManaged = await prisma.lead.count({
        where: {
          venueId,
          assignedToId: venueUser.userId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      const conversions = await prisma.lead.count({
        where: {
          venueId,
          assignedToId: venueUser.userId,
          stage: 'won',
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      const conversionRate = leadsManaged > 0 ? (conversions / leadsManaged) * 100 : 0;

      return {
        user: venueUser.user,
        inquiriesHandled,
        leadsManaged,
        conversions,
        conversionRate,
      };
    })
  );

  return userStats.filter(stat => stat.inquiriesHandled > 0 || stat.leadsManaged > 0);
}

/**
 * Helper function to get timeline data
 */
async function getTimelineData(venueId: string, startDate: Date, endDate: Date, period: string) {
  const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  const timelineData = [];

  for (let i = 0; i < days; i++) {
    const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);
    const nextDate = new Date(date.getTime() + 24 * 60 * 60 * 1000);

    const inquiries = await prisma.inquiry.count({
      where: {
        venueId,
        createdAt: {
          gte: date,
          lt: nextDate,
        },
      },
    });

    const leads = await prisma.lead.count({
      where: {
        venueId,
        createdAt: {
          gte: date,
          lt: nextDate,
        },
      },
    });

    const conversions = await prisma.lead.count({
      where: {
        venueId,
        stage: 'won',
        updatedAt: {
          gte: date,
          lt: nextDate,
        },
      },
    });

    timelineData.push({
      date,
      inquiries,
      leads,
      conversions,
    });
  }

  return timelineData;
}

/**
 * Analytics resolvers
 * Provides real analytics data for venue dashboards
 */
export const analyticsResolvers = {
  // Query resolvers
  Query: {
    /**
     * Get analytics types
     */
    analyticsTypes: async (_: any, __: any, context: Context) => {
      try {
        return prisma.analyticsType.findMany({
          orderBy: { name: 'asc' },
        });
      } catch (error) {
        logger.error('Error fetching analytics types:', error);
        throw error;
      }
    },

    /**
     * Get analytics events with filtering and pagination
     */
    analyticsEvents: async (_: any, { venueId, filter, pagination }: any, context: Context) => {
      try {
        // Default pagination values
        const { page = 1, limit = 10 } = pagination || {};
        const skip = (page - 1) * limit;
        
        // Build filter conditions
        const where: any = { venueId };
        
        if (filter?.typeId) {
          where.typeId = filter.typeId;
        }
        
        if (filter?.category) {
          where.type = {
            category: filter.category,
          };
        }
        
        if (filter?.entityType) {
          where.entityType = filter.entityType;
        }
        
        if (filter?.entityId) {
          where.entityId = filter.entityId;
        }
        
        if (filter?.userId) {
          where.userId = filter.userId;
        }
        
        if (filter?.startDate || filter?.endDate) {
          where.occurredAt = {};
          if (filter.startDate) {
            where.occurredAt.gte = new Date(filter.startDate);
          }
          if (filter.endDate) {
            where.occurredAt.lte = new Date(filter.endDate);
          }
        }
        
        // Get total count
        const totalCount = await prisma.analyticsEvent.count({ where });
        
        // Get events
        const events = await prisma.analyticsEvent.findMany({
          where,
          skip,
          take: limit,
          orderBy: { occurredAt: 'desc' },
          include: {
            venue: true,
            type: true,
            user: true,
          },
        });
        
        // Create edges and page info
        const edges = events.map((event) => ({
          node: event,
          cursor: Buffer.from(`analytics-event-${event.id}`).toString('base64'),
        }));
        
        const pageInfo = {
          hasNextPage: skip + events.length < totalCount,
          hasPreviousPage: skip > 0,
          startCursor: edges.length > 0 ? edges[0].cursor : null,
          endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : null,
          totalCount,
        };
        
        return {
          edges,
          pageInfo,
        };
      } catch (error) {
        logger.error(`Error fetching analytics events for venue ${venueId}:`, error);
        throw error;
      }
    },

    /**
     * Get audit logs with filtering and pagination
     */
    auditLogs: async (_: any, { venueId, filter, pagination }: any, context: Context) => {
      try {
        // Default pagination values
        const { page = 1, limit = 10 } = pagination || {};
        const skip = (page - 1) * limit;
        
        // Build filter conditions
        const where: any = { venueId };
        
        if (filter?.action) {
          where.action = filter.action;
        }
        
        if (filter?.entityType) {
          where.entityType = filter.entityType;
        }
        
        if (filter?.entityId) {
          where.entityId = filter.entityId;
        }
        
        if (filter?.userId) {
          where.userId = filter.userId;
        }
        
        if (filter?.startDate || filter?.endDate) {
          where.createdAt = {};
          if (filter.startDate) {
            where.createdAt.gte = new Date(filter.startDate);
          }
          if (filter.endDate) {
            where.createdAt.lte = new Date(filter.endDate);
          }
        }
        
        // Get total count
        const totalCount = await prisma.auditLog.count({ where });
        
        // Get audit logs
        const auditLogs = await prisma.auditLog.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            user: true,
            venue: true,
          },
        });
        
        // Create edges and page info
        const edges = auditLogs.map((auditLog) => ({
          node: auditLog,
          cursor: Buffer.from(`audit-log-${auditLog.id}`).toString('base64'),
        }));
        
        const pageInfo = {
          hasNextPage: skip + auditLogs.length < totalCount,
          hasPreviousPage: skip > 0,
          startCursor: edges.length > 0 ? edges[0].cursor : null,
          endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : null,
          totalCount,
        };
        
        return {
          edges,
          pageInfo,
        };
      } catch (error) {
        logger.error(`Error fetching audit logs for venue ${venueId}:`, error);
        throw error;
      }
    },

    /**
     * Get analytics overview - the main dashboard data
     */
    analyticsOverview: async (_: any, { venueId, period }: any, context: Context) => {
      try {
        // Calculate date range based on period
        const now = new Date();
        let startDate: Date;

        switch (period) {
          case 'today':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            break;
          case 'week':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case 'month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            break;
          case 'quarter':
            const quarterStart = Math.floor(now.getMonth() / 3) * 3;
            startDate = new Date(now.getFullYear(), quarterStart, 1);
            break;
          case 'year':
            startDate = new Date(now.getFullYear(), 0, 1);
            break;
          default:
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // Default to 30 days
        }

        // Get inquiry stats
        const inquiryStats = await getInquiryStats(venueId, startDate, now);

        // Get lead stats
        const leadStats = await getLeadStats(venueId, startDate, now);

        // Get conversion stats
        const conversionStats = await getConversionStats(venueId, startDate, now);

        // Get top sources
        const topSources = await getTopSources(venueId, startDate, now);

        // Get event type distribution
        const eventTypeDistribution = await getEventTypeDistribution(venueId, startDate, now);

        // Get lead stage distribution
        const leadStageDistribution = await getLeadStageDistribution(venueId, startDate, now);

        // Get user performance
        const userPerformance = await getUserPerformance(venueId, startDate, now);

        // Get timeline data
        const timelineData = await getTimelineData(venueId, startDate, now, period);

        return {
          period,
          inquiryStats,
          leadStats,
          conversionStats,
          topSources,
          eventTypeDistribution,
          leadStageDistribution,
          userPerformance,
          timelineData,
        };
      } catch (error) {
        logger.error(`Error fetching analytics overview for venue ${venueId}:`, error);
        throw error;
      }
    },

    /**
     * Get inquiry report
     */
    inquiryReport: async (_: any, { venueId, startDate, endDate }: any, context: Context) => {
      try {
        const start = new Date(startDate);
        const end = new Date(endDate);

        const inquiries = await prisma.inquiry.findMany({
          where: {
            venueId,
            createdAt: {
              gte: start,
              lte: end,
            },
          },
          include: {
            messages: true,
            lead: true,
          },
        });

        return {
          totalInquiries: inquiries.length,
          inquiries: inquiries.map(inquiry => ({
            id: inquiry.id,
            subject: inquiry.subject,
            status: inquiry.status,
            createdAt: inquiry.createdAt,
            messageCount: inquiry.messages.length,
            hasLead: !!inquiry.lead,
          })),
        };
      } catch (error) {
        logger.error(`Error generating inquiry report for venue ${venueId}:`, error);
        throw error;
      }
    },

    /**
     * Get lead report
     */
    leadReport: async (_: any, { venueId, startDate, endDate }: any, context: Context) => {
      try {
        const start = new Date(startDate);
        const end = new Date(endDate);

        const leads = await prisma.lead.findMany({
          where: {
            venueId,
            createdAt: {
              gte: start,
              lte: end,
            },
          },
          include: {
            activities: true,
            tasks: true,
          },
        });

        return {
          totalLeads: leads.length,
          leads: leads.map(lead => ({
            id: lead.id,
            contactName: lead.contactName,
            status: lead.status,
            stage: lead.stage,
            score: lead.score,
            eventDate: lead.eventDate,
            guestCount: lead.guestCount,
            budgetRange: lead.budgetRange,
            createdAt: lead.createdAt,
            activityCount: lead.activities.length,
            taskCount: lead.tasks.length,
          })),
        };
      } catch (error) {
        logger.error(`Error generating lead report for venue ${venueId}:`, error);
        throw error;
      }
    },
  },
};
