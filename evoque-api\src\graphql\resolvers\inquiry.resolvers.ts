import { prisma } from '../../config/database';
import { logger } from '../../utils/logger';
import { Context } from '../context';
import { AIService } from '../../services/ai.service';
import { AnalyticsService } from '../../services/analytics.service';

/**
 * Inquiry resolvers
 */
export const inquiryResolvers = {
  // Query resolvers
  Query: {
    /**
     * Get inquiry by ID
     */
    inquiry: async (_: any, { id }: any, context: Context) => {
      try {
        return prisma.inquiry.findUnique({
          where: { id },
          include: {
            venue: true,
            assignedTo: true,
            messages: true,
            lead: true,
          },
        });
      } catch (error) {
        logger.error(`Error fetching inquiry ${id}:`, error);
        throw error;
      }
    },

    /**
     * Get inquiries with filtering and pagination
     */
    inquiries: async (_: any, { venueId, filter, pagination }: any, context: Context) => {
      try {
        // Default pagination values
        const { page = 1, limit = 10 } = pagination || {};
        const skip = (page - 1) * limit;
        
        // Build filter conditions
        const where: any = { venueId };
        
        if (filter?.status) {
          where.status = filter.status;
        }
        
        if (filter?.priority) {
          where.priority = filter.priority;
        }
        
        if (filter?.assignedToId) {
          where.assignedToId = filter.assignedToId;
        }
        
        if (filter?.source) {
          where.source = filter.source;
        }
        
        if (filter?.search) {
          where.OR = [
            { subject: { contains: filter.search, mode: 'insensitive' } },
            { contactEmail: { contains: filter.search, mode: 'insensitive' } },
            { contactName: { contains: filter.search, mode: 'insensitive' } },
          ];
        }
        
        if (filter?.startDate || filter?.endDate) {
          where.createdAt = {};
          if (filter.startDate) {
            where.createdAt.gte = new Date(filter.startDate);
          }
          if (filter.endDate) {
            where.createdAt.lte = new Date(filter.endDate);
          }
        }
        
        // Get total count
        const totalCount = await prisma.inquiry.count({ where });
        
        // Get inquiries
        const inquiries = await prisma.inquiry.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            venue: true,
            assignedTo: true,
            messages: true,
            lead: true,
          },
        });
        
        // Create edges and page info
        const edges = inquiries.map((inquiry) => ({
          node: inquiry,
          cursor: Buffer.from(`inquiry-${inquiry.id}`).toString('base64'),
        }));
        
        const pageInfo = {
          hasNextPage: skip + inquiries.length < totalCount,
          hasPreviousPage: skip > 0,
          startCursor: edges.length > 0 ? edges[0].cursor : null,
          endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : null,
          totalCount,
        };
        
        return {
          edges,
          pageInfo,
        };
      } catch (error) {
        logger.error(`Error fetching inquiries for venue ${venueId}:`, error);
        throw error;
      }
    },

    /**
     * Get inquiry messages
     */
    inquiryMessages: async (_: any, { inquiryId, pagination }: any, context: Context) => {
      try {
        // Default pagination values
        const { page = 1, limit = 50 } = pagination || {};
        const skip = (page - 1) * limit;
        
        // Get total count
        const totalCount = await prisma.inquiryMessage.count({
          where: { inquiryId },
        });
        
        // Get messages
        const messages = await prisma.inquiryMessage.findMany({
          where: { inquiryId },
          skip,
          take: limit,
          orderBy: { createdAt: 'asc' },
          include: {
            sender: true,
          },
        });
        
        // Create edges and page info
        const edges = messages.map((message) => ({
          node: message,
          cursor: Buffer.from(`inquiry-message-${message.id}`).toString('base64'),
        }));
        
        const pageInfo = {
          hasNextPage: skip + messages.length < totalCount,
          hasPreviousPage: skip > 0,
          startCursor: edges.length > 0 ? edges[0].cursor : null,
          endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : null,
          totalCount,
        };
        
        return {
          edges,
          pageInfo,
        };
      } catch (error) {
        logger.error(`Error fetching messages for inquiry ${inquiryId}:`, error);
        throw error;
      }
    },
  },

  // Mutation resolvers
  Mutation: {
    /**
     * Create a new inquiry
     */
    createInquiry: async (_: any, { input }: any, context: Context) => {
      try {
        const {
          venueId,
          subject,
          contactName,
          contactEmail,
          contactPhone,
          source,
          priority,
          metadata,
        } = input;

        const inquiry = await prisma.inquiry.create({
          data: {
            venueId,
            subject,
            contactName,
            contactEmail,
            contactPhone,
            source: source || 'website',
            priority: priority || 'medium',
            status: 'new',
            metadata: metadata || {},
          },
          include: {
            venue: true,
            assignedTo: true,
            messages: true,
            lead: true,
          },
        });

        // Track analytics
        await AnalyticsService.trackEvent(
          venueId,
          'inquiry-created',
          {
            inquiryId: inquiry.id,
            source: inquiry.source,
            priority: inquiry.priority,
          },
          context.user?.id,
          'inquiry',
          inquiry.id
        );

        // Track source attribution
        await AnalyticsService.trackSourceAttribution(
          venueId,
          inquiry.source || 'website',
          inquiry.id
        );

        return inquiry;
      } catch (error) {
        logger.error('Error creating inquiry:', error);
        throw error;
      }
    },

    /**
     * Update an inquiry
     */
    updateInquiry: async (_: any, { id, input }: any, context: Context) => {
      try {
        const inquiry = await prisma.inquiry.update({
          where: { id },
          data: input,
          include: {
            venue: true,
            assignedTo: true,
            messages: true,
            lead: true,
          },
        });

        return inquiry;
      } catch (error) {
        logger.error(`Error updating inquiry ${id}:`, error);
        throw error;
      }
    },

    /**
     * Delete an inquiry
     */
    deleteInquiry: async (_: any, { id }: any, context: Context) => {
      try {
        await prisma.inquiry.delete({
          where: { id },
        });

        return true;
      } catch (error) {
        logger.error(`Error deleting inquiry ${id}:`, error);
        throw error;
      }
    },

    /**
     * Assign inquiry to user
     */
    assignInquiry: async (_: any, { id, userId }: any, context: Context) => {
      try {
        const inquiry = await prisma.inquiry.update({
          where: { id },
          data: { assignedToId: userId },
          include: {
            venue: true,
            assignedTo: true,
            messages: true,
            lead: true,
          },
        });

        return inquiry;
      } catch (error) {
        logger.error(`Error assigning inquiry ${id} to user ${userId}:`, error);
        throw error;
      }
    },

    /**
     * Create inquiry message
     */
    createInquiryMessage: async (_: any, { input }: any, context: Context) => {
      try {
        const { inquiryId, content, senderType, senderId } = input;

        const message = await prisma.inquiryMessage.create({
          data: {
            inquiryId,
            content,
            senderType,
            senderId,
          },
          include: {
            inquiry: true,
            sender: true,
          },
        });

        // Update inquiry status if this is a response
        if (senderType === 'venue') {
          await prisma.inquiry.update({
            where: { id: inquiryId },
            data: { status: 'responded' },
          });
        }

        return message;
      } catch (error) {
        logger.error('Error creating inquiry message:', error);
        throw error;
      }
    },

    /**
     * Generate AI response for inquiry
     */
    generateAIResponse: async (_: any, { inquiryId }: any, context: Context) => {
      try {
        // Get the inquiry with messages
        const inquiry = await prisma.inquiry.findUnique({
          where: { id: inquiryId },
          include: {
            messages: {
              orderBy: { createdAt: 'desc' },
              take: 1,
            },
            venue: true,
          },
        });

        if (!inquiry) {
          throw new Error('Inquiry not found');
        }

        const lastMessage = inquiry.messages[0];
        if (!lastMessage) {
          throw new Error('No messages found for inquiry');
        }

        // Generate AI response
        const aiResponse = await AIService.generateResponse(
          inquiryId,
          lastMessage.content,
          true
        );

        // Create the response message
        const responseMessage = await prisma.inquiryMessage.create({
          data: {
            inquiryId,
            content: aiResponse.content,
            senderType: 'venue',
            senderId: context.user?.id,
            metadata: {
              aiGenerated: true,
              confidence: aiResponse.confidence,
            },
          },
          include: {
            inquiry: true,
            sender: true,
          },
        });

        // Update inquiry status
        await prisma.inquiry.update({
          where: { id: inquiryId },
          data: { status: 'responded' },
        });

        return responseMessage;
      } catch (error) {
        logger.error(`Error generating AI response for inquiry ${inquiryId}:`, error);
        throw error;
      }
    },
  },

  // Subscription resolvers
  Subscription: {
    // Add real-time subscriptions for inquiries if needed
  },

  // Type resolvers
  Inquiry: {
    venue: async (parent: any, _: any, context: Context) => {
      return prisma.venue.findUnique({
        where: { id: parent.venueId },
      });
    },

    assignedTo: async (parent: any, _: any, context: Context) => {
      if (!parent.assignedToId) return null;
      return prisma.user.findUnique({
        where: { id: parent.assignedToId },
      });
    },

    messages: async (parent: any, _: any, context: Context) => {
      return prisma.inquiryMessage.findMany({
        where: { inquiryId: parent.id },
        orderBy: { createdAt: 'asc' },
        include: {
          sender: true,
        },
      });
    },

    lead: async (parent: any, _: any, context: Context) => {
      return prisma.lead.findFirst({
        where: { inquiryId: parent.id },
      });
    },
  },
};
