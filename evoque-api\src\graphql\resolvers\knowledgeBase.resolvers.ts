import { prisma } from '../../config/database';
import { logger } from '../../utils/logger';
import { Context } from '../context';

/**
 * Knowledge Base resolvers
 */
export const knowledgeBaseResolvers = {
  // Query resolvers
  Query: {
    /**
     * Get knowledge base categories
     */
    knowledgeBaseCategories: async (_: any, { venueId, filter }: any, context: Context) => {
      try {
        const where: any = { venueId };
        
        if (filter?.parentCategoryId) {
          where.parentCategoryId = filter.parentCategoryId;
        }
        
        if (filter?.search) {
          where.OR = [
            { name: { contains: filter.search, mode: 'insensitive' } },
            { description: { contains: filter.search, mode: 'insensitive' } },
          ];
        }

        return prisma.kBCategory.findMany({
          where,
          orderBy: { orderIndex: 'asc' },
          include: {
            venue: true,
            parentCategory: true,
            subcategories: true,
            items: true,
          },
        });
      } catch (error) {
        logger.error(`Error fetching knowledge base categories for venue ${venueId}:`, error);
        throw error;
      }
    },

    /**
     * Get knowledge base category by ID
     */
    knowledgeBaseCategory: async (_: any, { id }: any, context: Context) => {
      try {
        return prisma.kBCategory.findUnique({
          where: { id },
          include: {
            venue: true,
            parentCategory: true,
            subcategories: true,
            items: true,
          },
        });
      } catch (error) {
        logger.error(`Error fetching knowledge base category ${id}:`, error);
        throw error;
      }
    },

    /**
     * Get knowledge base items with filtering and pagination
     */
    knowledgeBaseItems: async (_: any, { venueId, filter, pagination }: any, context: Context) => {
      try {
        // Default pagination values
        const { page = 1, limit = 10 } = pagination || {};
        const skip = (page - 1) * limit;
        
        // Build filter conditions
        const where: any = { venueId };
        
        if (filter?.categoryId) {
          where.categoryId = filter.categoryId;
        }
        
        if (filter?.status) {
          where.status = filter.status;
        }
        
        if (filter?.contentType) {
          where.contentType = filter.contentType;
        }
        
        if (filter?.createdById) {
          where.createdById = filter.createdById;
        }
        
        if (filter?.search) {
          where.OR = [
            { title: { contains: filter.search, mode: 'insensitive' } },
            { content: { contains: filter.search, mode: 'insensitive' } },
          ];
        }
        
        if (filter?.startDate || filter?.endDate) {
          where.createdAt = {};
          if (filter.startDate) {
            where.createdAt.gte = new Date(filter.startDate);
          }
          if (filter.endDate) {
            where.createdAt.lte = new Date(filter.endDate);
          }
        }
        
        // Get total count
        const totalCount = await prisma.knowledgeBaseItem.count({ where });
        
        // Get items
        const items = await prisma.knowledgeBaseItem.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            venue: true,
            category: true,
            createdBy: true,
            updatedBy: true,
            vectors: true,
            revisions: true,
          },
        });
        
        // Create edges and page info
        const edges = items.map((item) => ({
          node: item,
          cursor: Buffer.from(`kb-item-${item.id}`).toString('base64'),
        }));
        
        const pageInfo = {
          hasNextPage: skip + items.length < totalCount,
          hasPreviousPage: skip > 0,
          startCursor: edges.length > 0 ? edges[0].cursor : null,
          endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : null,
          totalCount,
        };
        
        return {
          edges,
          pageInfo,
        };
      } catch (error) {
        logger.error(`Error fetching knowledge base items for venue ${venueId}:`, error);
        throw error;
      }
    },

    /**
     * Get knowledge base item by ID
     */
    knowledgeBaseItem: async (_: any, { id }: any, context: Context) => {
      try {
        return prisma.knowledgeBaseItem.findUnique({
          where: { id },
          include: {
            venue: true,
            category: true,
            createdBy: true,
            updatedBy: true,
            vectors: true,
            revisions: true,
          },
        });
      } catch (error) {
        logger.error(`Error fetching knowledge base item ${id}:`, error);
        throw error;
      }
    },

    /**
     * Get knowledge base revisions
     */
    knowledgeBaseRevisions: async (_: any, { itemId }: any, context: Context) => {
      try {
        return prisma.knowledgeBaseRevision.findMany({
          where: { itemId },
          orderBy: { createdAt: 'desc' },
          include: {
            createdBy: true,
          },
        });
      } catch (error) {
        logger.error(`Error fetching revisions for knowledge base item ${itemId}:`, error);
        throw error;
      }
    },

    /**
     * Search knowledge base
     */
    searchKnowledgeBase: async (_: any, { venueId, query, limit = 10 }: any, context: Context) => {
      try {
        // Simple text search - could be enhanced with vector search
        const items = await prisma.knowledgeBaseItem.findMany({
          where: {
            venueId,
            status: 'published',
            OR: [
              { title: { contains: query, mode: 'insensitive' } },
              { content: { contains: query, mode: 'insensitive' } },
            ],
          },
          take: limit,
          include: {
            category: true,
          },
        });

        return items.map(item => ({
          item,
          score: 1.0, // Placeholder score
          snippet: item.content.substring(0, 200) + '...',
        }));
      } catch (error) {
        logger.error(`Error searching knowledge base for venue ${venueId}:`, error);
        throw error;
      }
    },
  },

  // Mutation resolvers
  Mutation: {
    /**
     * Create knowledge base category
     */
    createKBCategory: async (_: any, { input }: any, context: Context) => {
      try {
        const category = await prisma.kBCategory.create({
          data: input,
          include: {
            venue: true,
            parentCategory: true,
            subcategories: true,
            items: true,
          },
        });

        return category;
      } catch (error) {
        logger.error('Error creating knowledge base category:', error);
        throw error;
      }
    },

    /**
     * Update knowledge base category
     */
    updateKBCategory: async (_: any, { id, input }: any, context: Context) => {
      try {
        const category = await prisma.kBCategory.update({
          where: { id },
          data: input,
          include: {
            venue: true,
            parentCategory: true,
            subcategories: true,
            items: true,
          },
        });

        return category;
      } catch (error) {
        logger.error(`Error updating knowledge base category ${id}:`, error);
        throw error;
      }
    },

    /**
     * Delete knowledge base category
     */
    deleteKBCategory: async (_: any, { id }: any, context: Context) => {
      try {
        await prisma.kBCategory.delete({
          where: { id },
        });

        return true;
      } catch (error) {
        logger.error(`Error deleting knowledge base category ${id}:`, error);
        throw error;
      }
    },

    /**
     * Create knowledge base item
     */
    createKBItem: async (_: any, { input }: any, context: Context) => {
      try {
        const item = await prisma.knowledgeBaseItem.create({
          data: {
            ...input,
            status: input.status || 'draft',
            contentType: input.contentType || 'text',
            createdById: context.user?.id,
          },
          include: {
            venue: true,
            category: true,
            createdBy: true,
            updatedBy: true,
            vectors: true,
            revisions: true,
          },
        });

        return item;
      } catch (error) {
        logger.error('Error creating knowledge base item:', error);
        throw error;
      }
    },

    /**
     * Update knowledge base item
     */
    updateKBItem: async (_: any, { id, input }: any, context: Context) => {
      try {
        // Create revision before updating
        const existingItem = await prisma.knowledgeBaseItem.findUnique({
          where: { id },
        });

        if (existingItem) {
          await prisma.knowledgeBaseRevision.create({
            data: {
              itemId: id,
              title: existingItem.title,
              content: existingItem.content,
              contentType: existingItem.contentType,
              metadata: existingItem.metadata,
              createdById: context.user?.id,
            },
          });
        }

        const item = await prisma.knowledgeBaseItem.update({
          where: { id },
          data: {
            ...input,
            updatedById: context.user?.id,
          },
          include: {
            venue: true,
            category: true,
            createdBy: true,
            updatedBy: true,
            vectors: true,
            revisions: true,
          },
        });

        return item;
      } catch (error) {
        logger.error(`Error updating knowledge base item ${id}:`, error);
        throw error;
      }
    },

    /**
     * Delete knowledge base item
     */
    deleteKBItem: async (_: any, { id }: any, context: Context) => {
      try {
        await prisma.knowledgeBaseItem.delete({
          where: { id },
        });

        return true;
      } catch (error) {
        logger.error(`Error deleting knowledge base item ${id}:`, error);
        throw error;
      }
    },
  },

  // Type resolvers
  KnowledgeBaseCategory: {
    venue: async (parent: any, _: any, context: Context) => {
      return prisma.venue.findUnique({
        where: { id: parent.venueId },
      });
    },

    parentCategory: async (parent: any, _: any, context: Context) => {
      if (!parent.parentCategoryId) return null;
      return prisma.kBCategory.findUnique({
        where: { id: parent.parentCategoryId },
      });
    },

    subcategories: async (parent: any, _: any, context: Context) => {
      return prisma.kBCategory.findMany({
        where: { parentCategoryId: parent.id },
        orderBy: { orderIndex: 'asc' },
      });
    },

    items: async (parent: any, _: any, context: Context) => {
      return prisma.knowledgeBaseItem.findMany({
        where: { categoryId: parent.id },
        orderBy: { createdAt: 'desc' },
      });
    },
  },

  KnowledgeBaseItem: {
    venue: async (parent: any, _: any, context: Context) => {
      return prisma.venue.findUnique({
        where: { id: parent.venueId },
      });
    },

    category: async (parent: any, _: any, context: Context) => {
      if (!parent.categoryId) return null;
      return prisma.kBCategory.findUnique({
        where: { id: parent.categoryId },
      });
    },

    createdBy: async (parent: any, _: any, context: Context) => {
      if (!parent.createdById) return null;
      return prisma.user.findUnique({
        where: { id: parent.createdById },
      });
    },

    updatedBy: async (parent: any, _: any, context: Context) => {
      if (!parent.updatedById) return null;
      return prisma.user.findUnique({
        where: { id: parent.updatedById },
      });
    },

    vectors: async (parent: any, _: any, context: Context) => {
      return prisma.knowledgeBaseVector.findMany({
        where: { itemId: parent.id },
      });
    },

    revisions: async (parent: any, _: any, context: Context) => {
      return prisma.knowledgeBaseRevision.findMany({
        where: { itemId: parent.id },
        orderBy: { createdAt: 'desc' },
        include: {
          createdBy: true,
        },
      });
    },
  },
};
