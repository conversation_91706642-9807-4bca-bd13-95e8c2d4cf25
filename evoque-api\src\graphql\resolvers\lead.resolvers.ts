import { prisma } from '../../config/database';
import { logger } from '../../utils/logger';
import { Context } from '../context';
import { AIService } from '../../services/ai.service';

/**
 * Lead resolvers
 */
export const leadResolvers = {
  // Query resolvers
  Query: {
    /**
     * Get lead by ID
     */
    lead: async (_: any, { id }: any, context: Context) => {
      try {
        return prisma.lead.findUnique({
          where: { id },
          include: {
            venue: true,
            inquiry: true,
            assignedTo: true,
            activities: true,
            tasks: true,
            noteEntries: true,
          },
        });
      } catch (error) {
        logger.error(`Error fetching lead ${id}:`, error);
        throw error;
      }
    },

    /**
     * Get leads with filtering and pagination
     */
    leads: async (_: any, { venueId, filter, pagination }: any, context: Context) => {
      try {
        // Default pagination values
        const { page = 1, limit = 10 } = pagination || {};
        const skip = (page - 1) * limit;
        
        // Build filter conditions
        const where: any = { venueId };
        
        if (filter?.status) {
          where.status = filter.status;
        }
        
        if (filter?.stage) {
          where.stage = filter.stage;
        }
        
        if (filter?.assignedToId) {
          where.assignedToId = filter.assignedToId;
        }
        
        if (filter?.eventType) {
          where.eventType = filter.eventType;
        }
        
        if (filter?.minScore) {
          where.score = { gte: filter.minScore };
        }
        
        if (filter?.maxScore) {
          where.score = { ...where.score, lte: filter.maxScore };
        }
        
        if (filter?.search) {
          where.OR = [
            { contactName: { contains: filter.search, mode: 'insensitive' } },
            { contactEmail: { contains: filter.search, mode: 'insensitive' } },
            { contactPhone: { contains: filter.search, mode: 'insensitive' } },
          ];
        }
        
        if (filter?.eventDateStart || filter?.eventDateEnd) {
          where.eventDate = {};
          if (filter.eventDateStart) {
            where.eventDate.gte = new Date(filter.eventDateStart);
          }
          if (filter.eventDateEnd) {
            where.eventDate.lte = new Date(filter.eventDateEnd);
          }
        }
        
        if (filter?.startDate || filter?.endDate) {
          where.createdAt = {};
          if (filter.startDate) {
            where.createdAt.gte = new Date(filter.startDate);
          }
          if (filter.endDate) {
            where.createdAt.lte = new Date(filter.endDate);
          }
        }
        
        // Get total count
        const totalCount = await prisma.lead.count({ where });
        
        // Get leads
        const leads = await prisma.lead.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            venue: true,
            inquiry: true,
            assignedTo: true,
            activities: true,
            tasks: true,
            noteEntries: true,
          },
        });
        
        // Create edges and page info
        const edges = leads.map((lead) => ({
          node: lead,
          cursor: Buffer.from(`lead-${lead.id}`).toString('base64'),
        }));
        
        const pageInfo = {
          hasNextPage: skip + leads.length < totalCount,
          hasPreviousPage: skip > 0,
          startCursor: edges.length > 0 ? edges[0].cursor : null,
          endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : null,
          totalCount,
        };
        
        return {
          edges,
          pageInfo,
        };
      } catch (error) {
        logger.error(`Error fetching leads for venue ${venueId}:`, error);
        throw error;
      }
    },

    /**
     * Get lead activities
     */
    leadActivities: async (_: any, { leadId, pagination }: any, context: Context) => {
      try {
        // Default pagination values
        const { page = 1, limit = 20 } = pagination || {};
        const skip = (page - 1) * limit;
        
        // Get total count
        const totalCount = await prisma.leadActivity.count({
          where: { leadId },
        });
        
        // Get activities
        const activities = await prisma.leadActivity.findMany({
          where: { leadId },
          skip,
          take: limit,
          orderBy: { performedAt: 'desc' },
          include: {
            performedBy: true,
          },
        });
        
        // Create edges and page info
        const edges = activities.map((activity) => ({
          node: activity,
          cursor: Buffer.from(`lead-activity-${activity.id}`).toString('base64'),
        }));
        
        const pageInfo = {
          hasNextPage: skip + activities.length < totalCount,
          hasPreviousPage: skip > 0,
          startCursor: edges.length > 0 ? edges[0].cursor : null,
          endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : null,
          totalCount,
        };
        
        return {
          edges,
          pageInfo,
        };
      } catch (error) {
        logger.error(`Error fetching activities for lead ${leadId}:`, error);
        throw error;
      }
    },

    /**
     * Get lead tasks
     */
    leadTasks: async (_: any, { leadId, filter, pagination }: any, context: Context) => {
      try {
        // Default pagination values
        const { page = 1, limit = 20 } = pagination || {};
        const skip = (page - 1) * limit;
        
        // Build filter conditions
        const where: any = { leadId };
        
        if (filter?.status) {
          where.status = filter.status;
        }
        
        if (filter?.priority) {
          where.priority = filter.priority;
        }
        
        if (filter?.assignedToId) {
          where.assignedToId = filter.assignedToId;
        }
        
        if (filter?.dueDate) {
          where.dueDate = {
            lte: new Date(filter.dueDate),
          };
        }
        
        // Get total count
        const totalCount = await prisma.leadTask.count({ where });
        
        // Get tasks
        const tasks = await prisma.leadTask.findMany({
          where,
          skip,
          take: limit,
          orderBy: { dueDate: 'asc' },
          include: {
            assignedTo: true,
            createdBy: true,
          },
        });
        
        // Create edges and page info
        const edges = tasks.map((task) => ({
          node: task,
          cursor: Buffer.from(`lead-task-${task.id}`).toString('base64'),
        }));
        
        const pageInfo = {
          hasNextPage: skip + tasks.length < totalCount,
          hasPreviousPage: skip > 0,
          startCursor: edges.length > 0 ? edges[0].cursor : null,
          endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : null,
          totalCount,
        };
        
        return {
          edges,
          pageInfo,
        };
      } catch (error) {
        logger.error(`Error fetching tasks for lead ${leadId}:`, error);
        throw error;
      }
    },

    /**
     * Get lead notes
     */
    leadNotes: async (_: any, { leadId, pagination }: any, context: Context) => {
      try {
        // Default pagination values
        const { page = 1, limit = 20 } = pagination || {};
        const skip = (page - 1) * limit;
        
        // Get total count
        const totalCount = await prisma.leadNote.count({
          where: { leadId },
        });
        
        // Get notes
        const notes = await prisma.leadNote.findMany({
          where: { leadId },
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            createdBy: true,
          },
        });
        
        // Create edges and page info
        const edges = notes.map((note) => ({
          node: note,
          cursor: Buffer.from(`lead-note-${note.id}`).toString('base64'),
        }));
        
        const pageInfo = {
          hasNextPage: skip + notes.length < totalCount,
          hasPreviousPage: skip > 0,
          startCursor: edges.length > 0 ? edges[0].cursor : null,
          endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : null,
          totalCount,
        };
        
        return {
          edges,
          pageInfo,
        };
      } catch (error) {
        logger.error(`Error fetching notes for lead ${leadId}:`, error);
        throw error;
      }
    },
  },

  // Mutation resolvers
  Mutation: {
    /**
     * Create a new lead
     */
    createLead: async (_: any, { input }: any, context: Context) => {
      try {
        const lead = await prisma.lead.create({
          data: {
            ...input,
            status: input.status || 'new',
            stage: input.stage || 'new',
          },
          include: {
            venue: true,
            inquiry: true,
            assignedTo: true,
            activities: true,
            tasks: true,
            noteEntries: true,
          },
        });

        // Generate AI lead score
        if (lead.id) {
          try {
            await AIService.scoreLeadWithAI(lead.id);
          } catch (error) {
            logger.warn(`Failed to generate AI score for lead ${lead.id}:`, error);
          }
        }

        return lead;
      } catch (error) {
        logger.error('Error creating lead:', error);
        throw error;
      }
    },

    /**
     * Update a lead
     */
    updateLead: async (_: any, { id, input }: any, context: Context) => {
      try {
        const lead = await prisma.lead.update({
          where: { id },
          data: input,
          include: {
            venue: true,
            inquiry: true,
            assignedTo: true,
            activities: true,
            tasks: true,
            noteEntries: true,
          },
        });

        return lead;
      } catch (error) {
        logger.error(`Error updating lead ${id}:`, error);
        throw error;
      }
    },

    /**
     * Delete a lead
     */
    deleteLead: async (_: any, { id }: any, context: Context) => {
      try {
        await prisma.lead.delete({
          where: { id },
        });

        return true;
      } catch (error) {
        logger.error(`Error deleting lead ${id}:`, error);
        throw error;
      }
    },
  },

  // Subscription resolvers
  Subscription: {
    // Add real-time subscriptions for leads if needed
  },

  // Type resolvers
  Lead: {
    venue: async (parent: any, _: any, context: Context) => {
      return prisma.venue.findUnique({
        where: { id: parent.venueId },
      });
    },

    inquiry: async (parent: any, _: any, context: Context) => {
      if (!parent.inquiryId) return null;
      return prisma.inquiry.findUnique({
        where: { id: parent.inquiryId },
      });
    },

    assignedTo: async (parent: any, _: any, context: Context) => {
      if (!parent.assignedToId) return null;
      return prisma.user.findUnique({
        where: { id: parent.assignedToId },
      });
    },

    activities: async (parent: any, _: any, context: Context) => {
      return prisma.leadActivity.findMany({
        where: { leadId: parent.id },
        orderBy: { performedAt: 'desc' },
        include: {
          performedBy: true,
        },
      });
    },

    tasks: async (parent: any, _: any, context: Context) => {
      return prisma.leadTask.findMany({
        where: { leadId: parent.id },
        orderBy: { dueDate: 'asc' },
        include: {
          assignedTo: true,
          createdBy: true,
        },
      });
    },

    noteEntries: async (parent: any, _: any, context: Context) => {
      return prisma.leadNote.findMany({
        where: { leadId: parent.id },
        orderBy: { createdAt: 'desc' },
        include: {
          createdBy: true,
        },
      });
    },
  },

  LeadTask: {
    lead: async (parent: any, _: any, context: Context) => {
      return prisma.lead.findUnique({
        where: { id: parent.leadId },
      });
    },

    assignedTo: async (parent: any, _: any, context: Context) => {
      if (!parent.assignedToId) return null;
      return prisma.user.findUnique({
        where: { id: parent.assignedToId },
      });
    },

    createdBy: async (parent: any, _: any, context: Context) => {
      if (!parent.createdById) return null;
      return prisma.user.findUnique({
        where: { id: parent.createdById },
      });
    },
  },
};
