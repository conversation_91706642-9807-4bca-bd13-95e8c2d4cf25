import { prisma } from '../../config/database';
import { logger } from '../../utils/logger';
import { Context } from '../context';

/**
 * Media resolvers
 */
export const mediaResolvers = {
  // Query resolvers
  Query: {
    /**
     * Get media categories
     */
    mediaCategories: async (_: any, { venueId, filter }: any, context: Context) => {
      try {
        const where: any = { venueId };
        
        if (filter?.search) {
          where.OR = [
            { name: { contains: filter.search, mode: 'insensitive' } },
            { description: { contains: filter.search, mode: 'insensitive' } },
          ];
        }

        return prisma.mediaCategory.findMany({
          where,
          orderBy: { name: 'asc' },
          include: {
            venue: true,
            assets: true,
          },
        });
      } catch (error) {
        logger.error(`Error fetching media categories for venue ${venueId}:`, error);
        throw error;
      }
    },

    /**
     * Get media category by ID
     */
    mediaCategory: async (_: any, { id }: any, context: Context) => {
      try {
        return prisma.mediaCategory.findUnique({
          where: { id },
          include: {
            venue: true,
            assets: true,
          },
        });
      } catch (error) {
        logger.error(`Error fetching media category ${id}:`, error);
        throw error;
      }
    },

    /**
     * Get media assets with filtering and pagination
     */
    mediaAssets: async (_: any, { venueId, filter, pagination }: any, context: Context) => {
      try {
        // Default pagination values
        const { page = 1, limit = 20 } = pagination || {};
        const skip = (page - 1) * limit;
        
        // Build filter conditions
        const where: any = { venueId };
        
        if (filter?.categoryId) {
          where.categoryId = filter.categoryId;
        }
        
        if (filter?.type) {
          where.type = filter.type;
        }
        
        if (filter?.status) {
          where.status = filter.status;
        }
        
        if (filter?.uploadedById) {
          where.uploadedById = filter.uploadedById;
        }
        
        if (filter?.search) {
          where.OR = [
            { filename: { contains: filter.search, mode: 'insensitive' } },
            { title: { contains: filter.search, mode: 'insensitive' } },
            { description: { contains: filter.search, mode: 'insensitive' } },
            { altText: { contains: filter.search, mode: 'insensitive' } },
          ];
        }
        
        if (filter?.startDate || filter?.endDate) {
          where.createdAt = {};
          if (filter.startDate) {
            where.createdAt.gte = new Date(filter.startDate);
          }
          if (filter.endDate) {
            where.createdAt.lte = new Date(filter.endDate);
          }
        }
        
        // Get total count
        const totalCount = await prisma.mediaAsset.count({ where });
        
        // Get assets
        const assets = await prisma.mediaAsset.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            venue: true,
            category: true,
            uploadedBy: true,
          },
        });
        
        // Create edges and page info
        const edges = assets.map((asset) => ({
          node: asset,
          cursor: Buffer.from(`media-asset-${asset.id}`).toString('base64'),
        }));
        
        const pageInfo = {
          hasNextPage: skip + assets.length < totalCount,
          hasPreviousPage: skip > 0,
          startCursor: edges.length > 0 ? edges[0].cursor : null,
          endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : null,
          totalCount,
        };
        
        return {
          edges,
          pageInfo,
        };
      } catch (error) {
        logger.error(`Error fetching media assets for venue ${venueId}:`, error);
        throw error;
      }
    },

    /**
     * Get media asset by ID
     */
    mediaAsset: async (_: any, { id }: any, context: Context) => {
      try {
        return prisma.mediaAsset.findUnique({
          where: { id },
          include: {
            venue: true,
            category: true,
            uploadedBy: true,
          },
        });
      } catch (error) {
        logger.error(`Error fetching media asset ${id}:`, error);
        throw error;
      }
    },
  },

  // Mutation resolvers
  Mutation: {
    /**
     * Create media category
     */
    createMediaCategory: async (_: any, { input }: any, context: Context) => {
      try {
        const category = await prisma.mediaCategory.create({
          data: input,
          include: {
            venue: true,
            assets: true,
          },
        });

        return category;
      } catch (error) {
        logger.error('Error creating media category:', error);
        throw error;
      }
    },

    /**
     * Update media category
     */
    updateMediaCategory: async (_: any, { id, input }: any, context: Context) => {
      try {
        const category = await prisma.mediaCategory.update({
          where: { id },
          data: input,
          include: {
            venue: true,
            assets: true,
          },
        });

        return category;
      } catch (error) {
        logger.error(`Error updating media category ${id}:`, error);
        throw error;
      }
    },

    /**
     * Delete media category
     */
    deleteMediaCategory: async (_: any, { id }: any, context: Context) => {
      try {
        await prisma.mediaCategory.delete({
          where: { id },
        });

        return true;
      } catch (error) {
        logger.error(`Error deleting media category ${id}:`, error);
        throw error;
      }
    },

    /**
     * Create media asset
     */
    createMediaAsset: async (_: any, { input }: any, context: Context) => {
      try {
        const asset = await prisma.mediaAsset.create({
          data: {
            ...input,
            uploadedById: context.user?.id,
            status: input.status || 'active',
          },
          include: {
            venue: true,
            category: true,
            uploadedBy: true,
          },
        });

        return asset;
      } catch (error) {
        logger.error('Error creating media asset:', error);
        throw error;
      }
    },

    /**
     * Update media asset
     */
    updateMediaAsset: async (_: any, { id, input }: any, context: Context) => {
      try {
        const asset = await prisma.mediaAsset.update({
          where: { id },
          data: input,
          include: {
            venue: true,
            category: true,
            uploadedBy: true,
          },
        });

        return asset;
      } catch (error) {
        logger.error(`Error updating media asset ${id}:`, error);
        throw error;
      }
    },

    /**
     * Delete media asset
     */
    deleteMediaAsset: async (_: any, { id }: any, context: Context) => {
      try {
        await prisma.mediaAsset.delete({
          where: { id },
        });

        return true;
      } catch (error) {
        logger.error(`Error deleting media asset ${id}:`, error);
        throw error;
      }
    },

    /**
     * Upload media asset
     */
    uploadMediaAsset: async (_: any, { input }: any, context: Context) => {
      try {
        // This would typically handle file upload to cloud storage
        // For now, we'll create a placeholder implementation
        const { venueId, categoryId, file, title, description, altText } = input;

        // In a real implementation, you would:
        // 1. Upload file to cloud storage (S3, Cloudinary, etc.)
        // 2. Get the URL and metadata
        // 3. Create the database record

        const asset = await prisma.mediaAsset.create({
          data: {
            venueId,
            categoryId,
            filename: file.filename || 'uploaded-file',
            originalFilename: file.filename || 'uploaded-file',
            mimeType: file.mimetype || 'application/octet-stream',
            size: file.size || 0,
            url: '/placeholder-url', // This would be the actual uploaded URL
            type: file.mimetype?.startsWith('image/') ? 'image' : 'document',
            title,
            description,
            altText,
            uploadedById: context.user?.id,
            status: 'active',
          },
          include: {
            venue: true,
            category: true,
            uploadedBy: true,
          },
        });

        return asset;
      } catch (error) {
        logger.error('Error uploading media asset:', error);
        throw error;
      }
    },
  },

  // Type resolvers
  MediaCategory: {
    venue: async (parent: any, _: any, context: Context) => {
      return prisma.venue.findUnique({
        where: { id: parent.venueId },
      });
    },

    assets: async (parent: any, _: any, context: Context) => {
      return prisma.mediaAsset.findMany({
        where: { categoryId: parent.id },
        orderBy: { createdAt: 'desc' },
      });
    },
  },

  MediaAsset: {
    venue: async (parent: any, _: any, context: Context) => {
      return prisma.venue.findUnique({
        where: { id: parent.venueId },
      });
    },

    category: async (parent: any, _: any, context: Context) => {
      if (!parent.categoryId) return null;
      return prisma.mediaCategory.findUnique({
        where: { id: parent.categoryId },
      });
    },

    uploadedBy: async (parent: any, _: any, context: Context) => {
      if (!parent.uploadedById) return null;
      return prisma.user.findUnique({
        where: { id: parent.uploadedById },
      });
    },
  },
};
