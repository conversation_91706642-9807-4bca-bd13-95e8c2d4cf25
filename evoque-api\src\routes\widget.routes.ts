import { Router } from 'express';
import { prisma } from '../config/database';
import { logger } from '../utils/logger';

const router = Router();

/**
 * Auto-detect venue from domain
 */
router.get('/detect', async (req, res) => {
  try {
    const { domain } = req.query;
    
    if (!domain || typeof domain !== 'string') {
      return res.status(400).json({ error: 'Domain parameter is required' });
    }

    // Clean domain (remove www, protocols, etc.)
    const cleanDomain = domain
      .replace(/^https?:\/\//, '')
      .replace(/^www\./, '')
      .toLowerCase()
      .trim();

    // Look for venue with matching domain
    const venue = await prisma.venue.findFirst({
      where: {
        OR: [
          { website: { contains: cleanDomain } },
          { website: { contains: domain } },
          { customDomain: cleanDomain },
        ],
      },
      select: {
        id: true,
        name: true,
        website: true,
        customDomain: true,
      },
    });

    if (venue) {
      return res.json({
        venueId: venue.id,
        venueName: venue.name,
        detected: true,
      });
    }

    // If no exact match, try partial matching
    const partialMatch = await prisma.venue.findFirst({
      where: {
        OR: [
          { website: { contains: cleanDomain.split('.')[0] } },
          { name: { contains: cleanDomain.split('.')[0], mode: 'insensitive' } },
        ],
      },
      select: {
        id: true,
        name: true,
        website: true,
      },
    });

    if (partialMatch) {
      return res.json({
        venueId: partialMatch.id,
        venueName: partialMatch.name,
        detected: true,
        confidence: 'partial',
      });
    }

    return res.json({
      detected: false,
      message: 'No venue found for this domain',
    });

  } catch (error) {
    logger.error('Error detecting venue from domain:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Track widget installation analytics
 */
router.post('/installation', async (req, res) => {
  try {
    const {
      venueId,
      domain,
      platform,
      status,
      data,
      timestamp,
    } = req.body;

    // Create analytics event
    await prisma.analyticsEvent.create({
      data: {
        venueId,
        typeId: 'widget-installation', // This would need to be a valid analytics type ID
        entityType: 'widget',
        data: {
          domain,
          platform,
          status,
          installationData: data,
          timestamp,
        },
        occurredAt: new Date(timestamp || Date.now()),
      },
    });

    res.json({ success: true });

  } catch (error) {
    logger.error('Error tracking widget installation:', error);
    // Don't fail the request for analytics errors
    res.json({ success: false, error: 'Analytics tracking failed' });
  }
});

/**
 * Track widget load analytics
 */
router.post('/load', async (req, res) => {
  try {
    const {
      venueId,
      type,
      domain,
    } = req.body;

    // Create analytics event
    await prisma.analyticsEvent.create({
      data: {
        venueId,
        typeId: 'widget-load', // This would need to be a valid analytics type ID
        entityType: 'widget',
        data: {
          type, // 'standard' or 'iframe'
          domain,
          userAgent: req.headers['user-agent'],
          ip: req.ip,
        },
        occurredAt: new Date(),
      },
    });

    res.json({ success: true });

  } catch (error) {
    logger.error('Error tracking widget load:', error);
    // Don't fail the request for analytics errors
    res.json({ success: false, error: 'Analytics tracking failed' });
  }
});

/**
 * Simple chat endpoint for iframe fallback
 */
router.post('/message', async (req, res) => {
  try {
    const {
      venueId,
      message,
      source,
    } = req.body;

    if (!venueId || !message) {
      return res.status(400).json({ error: 'venueId and message are required' });
    }

    // Create inquiry
    const inquiry = await prisma.inquiry.create({
      data: {
        venueId,
        subject: 'Widget Chat Message',
        contactName: 'Website Visitor',
        contactEmail: '', // Would be collected in follow-up
        source: source || 'widget',
        status: 'new',
        priority: 'medium',
      },
    });

    // Create message
    await prisma.inquiryMessage.create({
      data: {
        inquiryId: inquiry.id,
        content: message,
        senderType: 'user',
      },
    });

    // Simple auto-response (in production, this would use AI)
    const autoResponse = `Thank you for your message! We've received your inquiry and will get back to you soon. 

In the meantime, feel free to browse our venue information or ask any specific questions about your wedding plans.

Best regards,
The ${inquiry.venue?.name || 'Venue'} Team`;

    await prisma.inquiryMessage.create({
      data: {
        inquiryId: inquiry.id,
        content: autoResponse,
        senderType: 'venue',
        metadata: {
          autoGenerated: true,
        },
      },
    });

    res.json({
      success: true,
      response: autoResponse,
      inquiryId: inquiry.id,
    });

  } catch (error) {
    logger.error('Error handling widget message:', error);
    res.status(500).json({ 
      error: 'Failed to send message',
      response: 'Sorry, there was an error sending your message. Please try again or contact us directly.',
    });
  }
});

/**
 * Get widget configuration for a venue
 */
router.get('/config/:venueId', async (req, res) => {
  try {
    const { venueId } = req.params;

    const venue = await prisma.venue.findUnique({
      where: { id: venueId },
      select: {
        id: true,
        name: true,
        primaryColor: true,
        website: true,
        phone: true,
        email: true,
        description: true,
      },
    });

    if (!venue) {
      return res.status(404).json({ error: 'Venue not found' });
    }

    // Return widget configuration
    res.json({
      venueId: venue.id,
      venueName: venue.name,
      primaryColor: venue.primaryColor || '#6366F1',
      welcomeMessage: `Hello! Welcome to ${venue.name}. How can I help you with your wedding venue inquiry?`,
      contactInfo: {
        phone: venue.phone,
        email: venue.email,
        website: venue.website,
      },
      description: venue.description,
    });

  } catch (error) {
    logger.error('Error fetching widget config:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;
