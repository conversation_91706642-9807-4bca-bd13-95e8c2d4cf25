-- Analytics Types Seed Data
-- This file creates the basic analytics types needed for the Evoque platform

INSERT INTO analytics_types (id, name, category, description, created_at) VALUES
-- Widget Analytics
('550e8400-e29b-41d4-a716-446655440001', 'widget-installation', 'widget', 'Widget installation tracking', NOW()),
('550e8400-e29b-41d4-a716-446655440002', 'widget-load', 'widget', 'Widget load event tracking', NOW()),
('550e8400-e29b-41d4-a716-446655440003', 'widget-opened', 'widget', 'Widget chat opened by user', NOW()),
('550e8400-e29b-41d4-a716-446655440004', 'widget-closed', 'widget', 'Widget chat closed by user', NOW()),
('550e8400-e29b-41d4-a716-446655440005', 'widget-message-sent', 'widget', 'Message sent through widget', NOW()),

-- Inquiry Analytics
('550e8400-e29b-41d4-a716-446655440010', 'inquiry-created', 'inquiry', 'New inquiry received', NOW()),
('550e8400-e29b-41d4-a716-446655440011', 'inquiry-responded', 'inquiry', 'Inquiry response sent', NOW()),
('550e8400-e29b-41d4-a716-446655440012', 'inquiry-assigned', 'inquiry', 'Inquiry assigned to user', NOW()),
('550e8400-e29b-41d4-a716-446655440013', 'inquiry-closed', 'inquiry', 'Inquiry marked as closed', NOW()),
('550e8400-e29b-41d4-a716-446655440014', 'inquiry-converted', 'inquiry', 'Inquiry converted to lead', NOW()),

-- Lead Analytics
('550e8400-e29b-41d4-a716-446655440020', 'lead-created', 'lead', 'New lead created', NOW()),
('550e8400-e29b-41d4-a716-446655440021', 'lead-updated', 'lead', 'Lead information updated', NOW()),
('550e8400-e29b-41d4-a716-446655440022', 'lead-stage-changed', 'lead', 'Lead stage progression', NOW()),
('550e8400-e29b-41d4-a716-446655440023', 'lead-scored', 'lead', 'Lead score calculated', NOW()),
('550e8400-e29b-41d4-a716-446655440024', 'lead-won', 'lead', 'Lead converted to booking', NOW()),
('550e8400-e29b-41d4-a716-446655440025', 'lead-lost', 'lead', 'Lead marked as lost', NOW()),

-- User Activity Analytics
('550e8400-e29b-41d4-a716-446655440030', 'user-login', 'user', 'User logged into system', NOW()),
('550e8400-e29b-41d4-a716-446655440031', 'user-logout', 'user', 'User logged out of system', NOW()),
('550e8400-e29b-41d4-a716-446655440032', 'user-action', 'user', 'General user action tracking', NOW()),

-- Revenue Analytics
('550e8400-e29b-41d4-a716-446655440040', 'booking-created', 'revenue', 'New booking created', NOW()),
('550e8400-e29b-41d4-a716-446655440041', 'booking-confirmed', 'revenue', 'Booking confirmed by venue', NOW()),
('550e8400-e29b-41d4-a716-446655440042', 'payment-received', 'revenue', 'Payment received for booking', NOW()),
('550e8400-e29b-41d4-a716-446655440043', 'booking-cancelled', 'revenue', 'Booking cancelled', NOW()),

-- Performance Analytics
('550e8400-e29b-41d4-a716-446655440050', 'response-time', 'performance', 'Response time measurement', NOW()),
('550e8400-e29b-41d4-a716-446655440051', 'page-view', 'performance', 'Page view tracking', NOW()),
('550e8400-e29b-41d4-a716-446655440052', 'conversion-event', 'performance', 'Conversion milestone reached', NOW()),

-- AI Analytics
('550e8400-e29b-41d4-a716-446655440060', 'ai-response-generated', 'ai', 'AI response generated', NOW()),
('550e8400-e29b-41d4-a716-446655440061', 'ai-score-calculated', 'ai', 'AI lead score calculated', NOW()),
('550e8400-e29b-41d4-a716-446655440062', 'ai-recommendation', 'ai', 'AI recommendation provided', NOW()),

-- Marketing Analytics
('550e8400-e29b-41d4-a716-446655440070', 'source-attribution', 'marketing', 'Traffic source attribution', NOW()),
('550e8400-e29b-41d4-a716-446655440071', 'campaign-interaction', 'marketing', 'Marketing campaign interaction', NOW()),
('550e8400-e29b-41d4-a716-446655440072', 'referral-tracked', 'marketing', 'Referral source tracked', NOW())

ON CONFLICT (id) DO NOTHING;
