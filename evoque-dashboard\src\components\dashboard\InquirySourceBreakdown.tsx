import React from 'react';
import { useQuery, gql } from '@apollo/client';
import { useAuth } from '@/context/AuthContext';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  ChartData,
  ChartOptions,
} from 'chart.js';
import { Doughnut } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

// Query to get actual inquiry sources
const INQUIRY_SOURCES_QUERY = gql`
  query InquirySources($venueId: ID!) {
    inquiries(venueId: $venueId, pagination: { first: 100 }) {
      edges {
        node {
          id
          source
          createdAt
        }
      }
    }
  }
`;

interface InquirySourceBreakdownProps {
  period: string;
}

const InquirySourceBreakdown: React.FC<InquirySourceBreakdownProps> = ({ period }) => {
  const { currentVenue } = useAuth();

  const { data, loading, error } = useQuery(INQUIRY_SOURCES_QUERY, {
    variables: {
      venueId: currentVenue?.venue.id,
    },
    skip: !currentVenue,
  });

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-600 mx-auto"></div>
        <p className="mt-2 text-sm text-gray-500">Loading sources...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-sm text-red-500">Error loading source data</p>
      </div>
    );
  }

  // Process the inquiry data to count sources
  const inquiries = data?.inquiries?.edges.map((edge: any) => edge.node) || [];
  const sourceCounts: { [key: string]: number } = {};
  
  inquiries.forEach((inquiry: any) => {
    const source = inquiry.source || 'Unknown';
    sourceCounts[source] = (sourceCounts[source] || 0) + 1;
  });

  // Convert to array and sort by count
  const sourceData = Object.entries(sourceCounts)
    .map(([source, count]) => ({
      source,
      count,
      percentage: inquiries.length > 0 ? (count / inquiries.length) * 100 : 0,
    }))
    .sort((a, b) => b.count - a.count);

  if (sourceData.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No inquiry data available</p>
      </div>
    );
  }

  // Prepare chart data
  const chartData: ChartData<'doughnut'> = {
    labels: sourceData.map(item => item.source),
    datasets: [
      {
        data: sourceData.map(item => item.count),
        backgroundColor: [
          '#3B82F6', // Blue
          '#10B981', // Green
          '#F59E0B', // Amber
          '#EF4444', // Red
          '#8B5CF6', // Purple
          '#06B6D4', // Cyan
          '#F97316', // Orange
          '#84CC16', // Lime
        ],
        borderWidth: 2,
        borderColor: '#ffffff',
      },
    ],
  };

  const chartOptions: ChartOptions<'doughnut'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          padding: 15,
          usePointStyle: true,
          font: {
            size: 12,
          },
        },
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const item = sourceData[context.dataIndex];
            return `${item.source}: ${item.count} (${item.percentage.toFixed(1)}%)`;
          }
        }
      }
    },
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900">Inquiry Sources</h3>
      
      {/* Chart */}
      <div className="h-64">
        <Doughnut data={chartData} options={chartOptions} />
      </div>

      {/* Source List */}
      <div className="space-y-2">
        {sourceData.map((item, index) => (
          <div key={item.source} className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-md">
            <div className="flex items-center">
              <div 
                className="w-3 h-3 rounded-full mr-3"
                style={{ backgroundColor: chartData.datasets[0].backgroundColor[index] }}
              />
              <span className="text-sm font-medium text-gray-900">{item.source}</span>
            </div>
            <div className="text-right">
              <div className="text-sm font-semibold text-gray-900">{item.count}</div>
              <div className="text-xs text-gray-500">{item.percentage.toFixed(1)}%</div>
            </div>
          </div>
        ))}
      </div>

      <div className="text-xs text-gray-500 text-center">
        Based on {inquiries.length} inquiries
      </div>
    </div>
  );
};

export default InquirySourceBreakdown;
