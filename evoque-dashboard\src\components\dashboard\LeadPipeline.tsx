import React from 'react';
import { useQuery, gql } from '@apollo/client';
import { useAuth } from '@/context/AuthContext';

// Query to get actual lead stages
const LEAD_PIPELINE_QUERY = gql`
  query LeadPipeline($venueId: ID!) {
    leads(venueId: $venueId, pagination: { first: 100 }) {
      edges {
        node {
          id
          stage
          status
          budgetRange
          createdAt
        }
      }
    }
  }
`;

interface LeadPipelineProps {
  period: string;
}

const LeadPipeline: React.FC<LeadPipelineProps> = ({ period }) => {
  const { currentVenue } = useAuth();

  const { data, loading, error } = useQuery(LEAD_PIPELINE_QUERY, {
    variables: {
      venueId: currentVenue?.venue.id,
    },
    skip: !currentVenue,
  });

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-600 mx-auto"></div>
        <p className="mt-2 text-sm text-gray-500">Loading pipeline...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-sm text-red-500">Error loading pipeline data</p>
      </div>
    );
  }

  // Process the lead data to count by stage
  const leads = data?.leads?.edges.map((edge: any) => edge.node) || [];
  const stageCounts: { [key: string]: number } = {};
  
  leads.forEach((lead: any) => {
    const stage = lead.stage || 'inquiry';
    stageCounts[stage] = (stageCounts[stage] || 0) + 1;
  });

  // Define standard pipeline stages
  const pipelineStages = [
    { key: 'inquiry', label: 'New Inquiry', color: 'bg-blue-500' },
    { key: 'qualified', label: 'Qualified', color: 'bg-yellow-500' },
    { key: 'proposal', label: 'Proposal Sent', color: 'bg-orange-500' },
    { key: 'negotiation', label: 'Negotiating', color: 'bg-purple-500' },
    { key: 'won', label: 'Won', color: 'bg-green-500' },
    { key: 'lost', label: 'Lost', color: 'bg-red-500' },
  ];

  const totalLeads = leads.length;

  if (totalLeads === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No leads in pipeline</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900">Lead Pipeline</h3>
      
      {/* Pipeline Visualization */}
      <div className="space-y-3">
        {pipelineStages.map((stage) => {
          const count = stageCounts[stage.key] || 0;
          const percentage = totalLeads > 0 ? (count / totalLeads) * 100 : 0;
          
          return (
            <div key={stage.key} className="relative">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm font-medium text-gray-700">{stage.label}</span>
                <span className="text-sm text-gray-500">{count} leads</span>
              </div>
              
              {/* Progress bar */}
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className={`h-3 rounded-full ${stage.color} transition-all duration-300`}
                  style={{ width: `${percentage}%` }}
                />
              </div>
              
              <div className="text-xs text-gray-500 mt-1">
                {percentage.toFixed(1)}% of total pipeline
              </div>
            </div>
          );
        })}
      </div>

      {/* Pipeline Summary */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Pipeline Summary</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Total Leads:</span>
            <span className="ml-2 font-semibold">{totalLeads}</span>
          </div>
          <div>
            <span className="text-gray-500">Active Leads:</span>
            <span className="ml-2 font-semibold">
              {(stageCounts.inquiry || 0) + (stageCounts.qualified || 0) + (stageCounts.proposal || 0) + (stageCounts.negotiation || 0)}
            </span>
          </div>
          <div>
            <span className="text-gray-500">Won:</span>
            <span className="ml-2 font-semibold text-green-600">{stageCounts.won || 0}</span>
          </div>
          <div>
            <span className="text-gray-500">Lost:</span>
            <span className="ml-2 font-semibold text-red-600">{stageCounts.lost || 0}</span>
          </div>
        </div>
      </div>

      <div className="text-xs text-gray-500 text-center">
        Based on {totalLeads} total leads
      </div>
    </div>
  );
};

export default LeadPipeline;
