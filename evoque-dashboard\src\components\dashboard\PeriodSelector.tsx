import React, { useState } from 'react';
import { CalendarIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import { format, subDays, subMonths, subYears, startOfDay, endOfDay } from 'date-fns';

interface PeriodSelectorProps {
  period: string;
  onPeriodChange: (period: string, customRange?: { start: Date; end: Date }) => void;
  showComparison?: boolean;
  onComparisonToggle?: (enabled: boolean) => void;
}

const PeriodSelector: React.FC<PeriodSelectorProps> = ({
  period,
  onPeriodChange,
  showComparison = false,
  onComparisonToggle,
}) => {
  const [showCustomPicker, setShowCustomPicker] = useState(false);
  const [customStartDate, setCustomStartDate] = useState('');
  const [customEndDate, setCustomEndDate] = useState('');
  const [comparisonEnabled, setComparisonEnabled] = useState(false);

  const predefinedPeriods = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
    { value: '1y', label: 'Last year' },
    { value: 'custom', label: 'Custom range' },
  ];

  const handlePredefinedPeriodChange = (newPeriod: string) => {
    if (newPeriod === 'custom') {
      setShowCustomPicker(true);
    } else {
      setShowCustomPicker(false);
      onPeriodChange(newPeriod);
    }
  };

  const handleCustomRangeApply = () => {
    if (customStartDate && customEndDate) {
      const startDate = startOfDay(new Date(customStartDate));
      const endDate = endOfDay(new Date(customEndDate));
      
      if (startDate <= endDate) {
        onPeriodChange('custom', { start: startDate, end: endDate });
        setShowCustomPicker(false);
      }
    }
  };

  const handleComparisonToggle = () => {
    const newValue = !comparisonEnabled;
    setComparisonEnabled(newValue);
    onComparisonToggle?.(newValue);
  };

  const getQuickRanges = () => {
    const today = new Date();
    return [
      {
        label: 'Today',
        start: startOfDay(today),
        end: endOfDay(today),
      },
      {
        label: 'Yesterday',
        start: startOfDay(subDays(today, 1)),
        end: endOfDay(subDays(today, 1)),
      },
      {
        label: 'This Week',
        start: startOfDay(subDays(today, today.getDay())),
        end: endOfDay(today),
      },
      {
        label: 'Last Week',
        start: startOfDay(subDays(today, today.getDay() + 7)),
        end: endOfDay(subDays(today, today.getDay() + 1)),
      },
      {
        label: 'This Month',
        start: startOfDay(new Date(today.getFullYear(), today.getMonth(), 1)),
        end: endOfDay(today),
      },
      {
        label: 'Last Month',
        start: startOfDay(subMonths(new Date(today.getFullYear(), today.getMonth(), 1), 1)),
        end: endOfDay(new Date(today.getFullYear(), today.getMonth(), 0)),
      },
    ];
  };

  const handleQuickRange = (range: { start: Date; end: Date }) => {
    setCustomStartDate(format(range.start, 'yyyy-MM-dd'));
    setCustomEndDate(format(range.end, 'yyyy-MM-dd'));
    onPeriodChange('custom', range);
    setShowCustomPicker(false);
  };

  return (
    <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
      {/* Period Selector */}
      <div className="flex items-center space-x-2">
        <div className="inline-flex rounded-md shadow-sm" role="group">
          {predefinedPeriods.map((p) => (
            <button
              key={p.value}
              type="button"
              onClick={() => handlePredefinedPeriodChange(p.value)}
              className={`px-4 py-2 text-sm font-medium ${
                (period === p.value) || (period === 'custom' && p.value === 'custom')
                  ? 'bg-primary-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              } ${
                p.value === '7d'
                  ? 'rounded-l-md'
                  : p.value === 'custom'
                  ? 'rounded-r-md'
                  : ''
              } border border-gray-300 focus:z-10 focus:ring-2 focus:ring-primary-500 focus:border-primary-500`}
            >
              {p.value === 'custom' && <CalendarIcon className="w-4 h-4 mr-1 inline" />}
              {p.label}
            </button>
          ))}
        </div>
      </div>

      {/* Comparison Toggle */}
      {showComparison && (
        <div className="flex items-center">
          <label className="flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={comparisonEnabled}
              onChange={handleComparisonToggle}
              className="sr-only"
            />
            <div className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              comparisonEnabled ? 'bg-primary-600' : 'bg-gray-200'
            }`}>
              <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                comparisonEnabled ? 'translate-x-6' : 'translate-x-1'
              }`} />
            </div>
            <span className="ml-2 text-sm text-gray-700">Compare to previous period</span>
          </label>
        </div>
      )}

      {/* Custom Date Picker Modal */}
      {showCustomPicker && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-screen items-center justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowCustomPicker(false)} />
            
            <div className="inline-block transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle">
              <div>
                <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-primary-100">
                  <CalendarIcon className="h-6 w-6 text-primary-600" />
                </div>
                <div className="mt-3 text-center sm:mt-5">
                  <h3 className="text-lg font-medium leading-6 text-gray-900">
                    Select Custom Date Range
                  </h3>
                  
                  {/* Quick Range Buttons */}
                  <div className="mt-4">
                    <p className="text-sm text-gray-500 mb-2">Quick ranges:</p>
                    <div className="grid grid-cols-2 gap-2">
                      {getQuickRanges().map((range) => (
                        <button
                          key={range.label}
                          onClick={() => handleQuickRange(range)}
                          className="px-3 py-2 text-xs bg-gray-100 hover:bg-gray-200 rounded-md text-gray-700"
                        >
                          {range.label}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Date Inputs */}
                  <div className="mt-4 space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 text-left">
                        Start Date
                      </label>
                      <input
                        type="date"
                        value={customStartDate}
                        onChange={(e) => setCustomStartDate(e.target.value)}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 text-left">
                        End Date
                      </label>
                      <input
                        type="date"
                        value={customEndDate}
                        onChange={(e) => setCustomEndDate(e.target.value)}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                <button
                  type="button"
                  onClick={handleCustomRangeApply}
                  disabled={!customStartDate || !customEndDate}
                  className="inline-flex w-full justify-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:bg-gray-300 disabled:cursor-not-allowed sm:col-start-2 sm:text-sm"
                >
                  Apply
                </button>
                <button
                  type="button"
                  onClick={() => setShowCustomPicker(false)}
                  className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 sm:col-start-1 sm:mt-0 sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PeriodSelector;
