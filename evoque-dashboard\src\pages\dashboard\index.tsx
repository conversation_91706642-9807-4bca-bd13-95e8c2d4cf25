import React, { useState } from 'react';
import { useQuery, gql } from '@apollo/client';
import { useAuth } from '@/context/AuthContext';
import {
  ChartBarIcon,
  ChatBubbleLeftRightIcon,
  ClockIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  TrophyIcon,
  CalendarDaysIcon,
  UsersIcon,
} from '@heroicons/react/24/outline';
import StatsCard from '@/components/dashboard/StatsCard';
import RecentInquiries from '@/components/dashboard/RecentInquiries';
import UpcomingEvents from '@/components/dashboard/UpcomingEvents';
import InquiryChart from '@/components/dashboard/InquiryChart';
import ConversionChart from '@/components/dashboard/ConversionChart';
import PeriodSelector from '@/components/dashboard/PeriodSelector';
import InquirySourceBreakdown from '@/components/dashboard/InquirySourceBreakdown';
import LeadPipeline from '@/components/dashboard/LeadPipeline';

// GraphQL query for dashboard data - using existing working query
const DASHBOARD_OVERVIEW_QUERY = gql`
  query DashboardOverview($venueId: ID!, $period: String!) {
    analyticsOverview(venueId: $venueId, period: $period) {
      period
      inquiryStats {
        total
        new
        responded
        avgResponseTime
      }
      leadStats {
        total
        new
        active
        won
        lost
        avgValue
      }
      conversionStats {
        inquiryToLeadRate
        leadToBookingRate
        overallConversionRate
      }
      timelineData {
        date
        inquiries
        leads
        conversions
      }
    }
    inquiries(venueId: $venueId, filter: { status: "new" }, pagination: { first: 5 }) {
      edges {
        node {
          id
          contactName
          contactEmail
          eventDate
          status
          source
          createdAt
        }
      }
    }
  }
`;

const Dashboard: React.FC = () => {
  const { currentVenue } = useAuth();
  const [period, setPeriod] = useState('30d'); // Default to last 30 days
  const [customDateRange, setCustomDateRange] = useState<{ start: Date; end: Date } | null>(null);
  const [comparisonEnabled, setComparisonEnabled] = useState(false);

  const { data, loading, error } = useQuery(DASHBOARD_OVERVIEW_QUERY, {
    variables: {
      venueId: currentVenue?.venue.id,
      period: customDateRange ? 'custom' : period,
      ...(customDateRange && {
        startDate: customDateRange.start.toISOString(),
        endDate: customDateRange.end.toISOString(),
      }),
    },
    skip: !currentVenue,
  });

  const handlePeriodChange = (newPeriod: string, customRange?: { start: Date; end: Date }) => {
    setPeriod(newPeriod);
    if (customRange) {
      setCustomDateRange(customRange);
    } else {
      setCustomDateRange(null);
    }
  };

  const handleComparisonToggle = (enabled: boolean) => {
    setComparisonEnabled(enabled);
    // TODO: Implement comparison data fetching
  };

  if (!currentVenue) {
    return (
      <div className="text-center py-12">
        <p className="text-lg text-gray-500">Please select a venue to continue.</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-danger-50 border-l-4 border-danger-400 p-4 mb-6">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-danger-800">Error loading dashboard data</h3>
            <div className="mt-2 text-sm text-danger-700">
              <p>{error.message}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const overview = data?.analyticsOverview;
  const recentInquiries = data?.inquiries?.edges.map((edge: any) => edge.node) || [];

  // Calculate actual metrics from real data
  const totalRevenue = (overview?.leadStats.won || 0) * (overview?.leadStats.avgValue || 0);
  const winRate = overview?.leadStats.total > 0
    ? ((overview?.leadStats.won / overview?.leadStats.total) * 100).toFixed(1)
    : '0';

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
        <p className="mt-1 text-sm text-gray-500">
          Overview of your venue's performance and recent activity.
        </p>
      </div>

      {/* Enhanced Period selector */}
      <div className="mb-6">
        <PeriodSelector
          period={period}
          onPeriodChange={handlePeriodChange}
          showComparison={true}
          onComparisonToggle={handleComparisonToggle}
        />
      </div>

      {/* Primary Stats cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
        <StatsCard
          title="New Inquiries"
          value={overview?.inquiryStats.new || 0}
          icon={ChatBubbleLeftRightIcon}
          change={10}
          trend="up"
          iconColor="bg-primary-500"
        />
        <StatsCard
          title="Conversion Rate"
          value={`${(overview?.conversionStats.inquiryToLeadRate * 100 || 0).toFixed(1)}%`}
          icon={ChartBarIcon}
          change={2.5}
          trend="up"
          iconColor="bg-success-500"
        />
        <StatsCard
          title="Avg. Response Time"
          value={`${overview?.inquiryStats.avgResponseTime || 0} min`}
          icon={ClockIcon}
          change={-15}
          trend="down"
          iconColor="bg-warning-500"
          trendPositive={true}
        />
        <StatsCard
          title="Avg. Booking Value"
          value={`$${overview?.leadStats.avgValue?.toLocaleString() || 0}`}
          icon={CurrencyDollarIcon}
          change={5}
          trend="up"
          iconColor="bg-secondary-500"
        />
      </div>

      {/* Additional useful metrics */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3 mb-6">
        <StatsCard
          title="Total Revenue"
          value={`$${totalRevenue.toLocaleString()}`}
          icon={TrophyIcon}
          iconColor="bg-emerald-500"
        />
        <StatsCard
          title="Active Leads"
          value={overview?.leadStats.active || 0}
          icon={UsersIcon}
          iconColor="bg-blue-500"
        />
        <StatsCard
          title="Win Rate"
          value={`${winRate}%`}
          icon={TrophyIcon}
          iconColor="bg-amber-500"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Inquiry Trends</h2>
          <InquiryChart data={overview?.timelineData || []} />
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Conversion Funnel</h2>
          <ConversionChart
            inquiries={overview?.inquiryStats.total || 0}
            leads={overview?.leadStats.total || 0}
            bookings={overview?.leadStats.won || 0}
          />
        </div>
      </div>

      {/* Useful Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow p-6">
          <InquirySourceBreakdown period={period} />
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <LeadPipeline period={period} />
        </div>
      </div>



      {/* Recent activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-5 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Recent Inquiries</h3>
          </div>
          <RecentInquiries inquiries={recentInquiries} />
        </div>
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-5 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Upcoming Events</h3>
          </div>
          <UpcomingEvents />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;