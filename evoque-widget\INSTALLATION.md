# Evoque Widget Installation Guide

## Universal Installation (Recommended)

The easiest way to install the Evoque widget on any website is using our universal script tag:

```html
<script src="https://cdn.evoque.digital/widget/v2/loader.js" 
        data-venue-id="your-venue-id"
        data-primary-color="#6366F1"
        data-welcome-message="Hello! How can I help you with your wedding venue inquiry?"
        async defer>
</script>
```

### Configuration Options

All configuration can be done via data attributes:

| Attribute | Default | Description |
|-----------|---------|-------------|
| `data-venue-id` | Required | Your unique venue identifier |
| `data-primary-color` | `#6366F1` | Widget primary color |
| `data-text-color` | `#FFFFFF` | Text color for buttons |
| `data-position` | `bottom-right` | Widget position (`bottom-right`, `bottom-left`, `top-right`, `top-left`) |
| `data-welcome-message` | Default message | Custom welcome message |
| `data-font-family` | `Arial, sans-serif` | Font family for widget text |
| `data-font-size` | `16px` | Base font size |
| `data-button-icon` | `chat` | Button icon (`chat`, `message`, `question`, `help`) |
| `data-show-branding` | `true` | Show Evoque branding (`true`, `false`) |
| `data-mobile-breakpoint` | `768` | Mobile breakpoint in pixels |
| `data-auto-detect` | `true` | Auto-detect venue from domain |
| `data-fallback-mode` | `false` | Use iframe fallback for restrictive platforms |
| `data-debug` | `false` | Enable debug logging |

## Platform-Specific Installation

### WordPress

#### Method 1: Plugin (Recommended)
1. Download the Evoque Widget plugin from WordPress.org
2. Install and activate the plugin
3. Go to Settings > Evoque Widget
4. Enter your venue ID and customize settings
5. The widget will automatically appear on all pages

#### Method 2: Theme Integration
Add to your theme's `functions.php`:

```php
function add_evoque_widget() {
    ?>
    <script src="https://cdn.evoque.digital/widget/v2/loader.js" 
            data-venue-id="your-venue-id"
            async defer>
    </script>
    <?php
}
add_action('wp_footer', 'add_evoque_widget');
```

#### Method 3: Widget Area
1. Go to Appearance > Widgets
2. Add a "Custom HTML" widget
3. Paste the universal script tag
4. Save and the widget will appear

### Wix

1. Go to your Wix Editor
2. Click "Add" > "More" > "HTML Code"
3. Paste the universal script tag
4. Position the HTML element anywhere (it will auto-position)
5. Publish your site

**Note**: Wix automatically uses iframe fallback mode for security.

### Squarespace

#### Method 1: Code Injection (Recommended)
1. Go to Settings > Advanced > Code Injection
2. Paste the script in the "Footer" section
3. Save and the widget will appear on all pages

#### Method 2: Page-Specific
1. Edit the page where you want the widget
2. Add a "Code" block
3. Paste the universal script tag
4. Save and publish

### Shopify

#### Method 1: App Store (Coming Soon)
Install the Evoque Widget app from the Shopify App Store.

#### Method 2: Theme Integration
1. Go to Online Store > Themes
2. Click "Actions" > "Edit code"
3. Open `theme.liquid`
4. Add the script before `</body>`
5. Save the file

```liquid
<script src="https://cdn.evoque.digital/widget/v2/loader.js" 
        data-venue-id="your-venue-id"
        async defer>
</script>
```

### Webflow

1. Go to Project Settings > Custom Code
2. Add the script to "Footer Code"
3. Publish your site

Or for page-specific installation:
1. Select the page
2. Go to Page Settings > Custom Code
3. Add to "Before </body> tag"

### Generic HTML/CSS Websites

Simply add the universal script tag before the closing `</body>` tag:

```html
<!DOCTYPE html>
<html>
<head>
    <title>Your Wedding Venue</title>
</head>
<body>
    <!-- Your website content -->
    
    <script src="https://cdn.evoque.digital/widget/v2/loader.js" 
            data-venue-id="your-venue-id"
            async defer>
    </script>
</body>
</html>
```

## Advanced Configuration

### JavaScript API

For advanced customization, you can use the JavaScript API:

```javascript
// Initialize with custom configuration
const widget = new EvoqueWidgetLoader({
    venueId: 'your-venue-id',
    primaryColor: '#6366F1',
    position: 'bottom-right',
    welcomeMessage: 'Welcome to our venue!',
    autoDetect: true,
    debug: false
});

// Access the widget instance
const widgetInstance = widget.getWidget();

// Open/close programmatically
widgetInstance.open();
widgetInstance.close();

// Update configuration
widgetInstance.updateOptions({
    primaryColor: '#ff6b6b',
    welcomeMessage: 'New message!'
});
```

### Custom Styling

Override widget styles with CSS:

```css
/* Customize widget button */
.evoque-widget-button {
    background: #your-color !important;
    border-radius: 10px !important;
}

/* Customize chat interface */
.evoque-widget-chat {
    font-family: 'Your Font', sans-serif !important;
}
```

### Event Handling

Listen to widget events:

```javascript
window.addEventListener('evoque-widget-opened', () => {
    console.log('Widget opened');
    // Track analytics, etc.
});

window.addEventListener('evoque-widget-closed', () => {
    console.log('Widget closed');
});

window.addEventListener('evoque-widget-message-sent', (event) => {
    console.log('Message sent:', event.detail);
});
```

## Troubleshooting

### Widget Not Appearing

1. **Check venue ID**: Ensure your venue ID is correct
2. **Check console**: Open browser dev tools and look for errors
3. **Enable debug mode**: Add `data-debug="true"` to see detailed logs
4. **Try fallback mode**: Add `data-fallback-mode="true"`

### Platform-Specific Issues

#### WordPress
- **Plugin conflicts**: Deactivate other chat plugins
- **Theme issues**: Try switching to a default theme temporarily
- **Caching**: Clear all caches after installation

#### Wix
- **Not showing**: Wix uses iframe mode automatically, which may take longer to load
- **Positioning issues**: The widget auto-positions regardless of HTML element placement

#### Squarespace
- **Style conflicts**: Add custom CSS to override theme styles
- **Mobile issues**: Check mobile preview and adjust breakpoint

### Performance Optimization

1. **Lazy loading**: The widget loads asynchronously by default
2. **Bundle size**: Iframe fallback uses a smaller bundle
3. **Caching**: Widget files are cached with long expiry times
4. **CDN**: Served from global CDN for fast loading

## Support

- **Documentation**: [docs.evoque.digital](https://docs.evoque.digital)
- **Support Email**: <EMAIL>
- **Live Chat**: Available on our website
- **GitHub Issues**: [github.com/evoque-digital/widget](https://github.com/evoque-digital/widget)

## Security

- **CSP Compatibility**: Widget works with Content Security Policy
- **HTTPS Only**: All resources served over HTTPS
- **No Tracking**: Widget respects user privacy
- **Data Protection**: GDPR and CCPA compliant
