# Evoque Widget - Squarespace Integration Guide

## Quick Installation (3 minutes)

### Method 1: Site-Wide Installation (Recommended)

1. **Access Developer Settings**
   - Log into your Squarespace account
   - Go to Settings → Advanced → Code Injection

2. **Add Widget Code**
   - Scroll to the "Footer" section
   - Paste this code:

```html
<script src="https://cdn.evoque.digital/widget/v2/loader.js" 
        data-venue-id="YOUR_VENUE_ID"
        data-primary-color="#6366F1"
        data-welcome-message="Hello! How can I help you with your wedding venue inquiry?"
        async defer>
</script>
```

3. **Configure Settings**
   - Replace `YOUR_VENUE_ID` with your actual venue ID
   - Customize colors to match your site theme
   - Adjust the welcome message for your venue

4. **Save and Test**
   - Click "Save" 
   - Visit your live site to test the widget
   - The widget will appear on all pages

### Method 2: Page-Specific Installation

1. **Edit Specific Page**
   - Go to the page where you want the widget
   - Click "Edit" on that page

2. **Add Code Block**
   - Click the "+" to add a new block
   - Choose "Code" from the block menu
   - Select "HTML" as the code type

3. **Insert Widget Code**
   - Paste the widget script (same as above)
   - Click outside the code block to save

4. **Position the Block**
   - You can place the code block anywhere on the page
   - We recommend placing it at the bottom
   - The widget will auto-position itself correctly

## Squarespace-Specific Optimizations

### Template Compatibility
The widget works with all Squarespace templates:
- ✅ 7.1 Templates (current)
- ✅ 7.0 Templates (legacy)
- ✅ Classic Templates
- ✅ All template families (Avenue, Bedford, etc.)

### Mobile Responsiveness
Automatically optimized for Squarespace mobile:
- Responsive design adapts to mobile screens
- Works with Squarespace's mobile styles
- Touch-friendly interface
- Fast loading on mobile devices

### Style Integration
The widget automatically adapts to your Squarespace theme:
- Inherits font families when possible
- Respects your site's color scheme
- Maintains visual consistency
- No conflicts with Squarespace CSS

## Advanced Customization

### Matching Your Brand Colors

Extract colors from your Squarespace theme:

1. **Find Your Theme Colors**
   - Go to Design → Site Styles
   - Note your accent colors and brand colors

2. **Apply to Widget**
```html
<script src="https://cdn.evoque.digital/widget/v2/loader.js" 
        data-venue-id="YOUR_VENUE_ID"
        data-primary-color="#YOUR_ACCENT_COLOR"
        data-text-color="#FFFFFF"
        data-font-family="YOUR_THEME_FONT"
        async defer>
</script>
```

### Custom CSS Integration

Add custom styles in Design → Custom CSS:

```css
/* Ensure widget appears above Squarespace elements */
.evoque-widget {
    z-index: 9999 !important;
}

/* Adjust for specific templates */
.sqs-layout .evoque-widget {
    position: fixed !important;
}

/* Mobile adjustments */
@media screen and (max-width: 768px) {
    .evoque-widget {
        bottom: 20px !important;
        right: 15px !important;
    }
}
```

### Template-Specific Adjustments

#### Avenue Template Family
```html
<script src="https://cdn.evoque.digital/widget/v2/loader.js" 
        data-venue-id="YOUR_VENUE_ID"
        data-primary-color="#000000"
        data-font-family="'Helvetica Neue', Arial, sans-serif"
        async defer>
</script>
```

#### Bedford Template Family
```html
<script src="https://cdn.evoque.digital/widget/v2/loader.js" 
        data-venue-id="YOUR_VENUE_ID"
        data-primary-color="#4a4a4a"
        data-font-family="'Proxima Nova', Arial, sans-serif"
        async defer>
</script>
```

## E-commerce Integration

### Squarespace Commerce
If you're selling wedding packages or services:

```html
<script src="https://cdn.evoque.digital/widget/v2/loader.js" 
        data-venue-id="YOUR_VENUE_ID"
        data-welcome-message="Interested in our wedding packages? Let's discuss your perfect day!"
        data-primary-color="#YOUR_BRAND_COLOR"
        async defer>
</script>
```

### Event Tracking
Track widget interactions with Squarespace Analytics:

```html
<script>
// Track widget events
window.addEventListener('evoque-widget-opened', function() {
    if (typeof gtag !== 'undefined') {
        gtag('event', 'widget_opened', {
            'event_category': 'engagement',
            'event_label': 'evoque_widget'
        });
    }
});
</script>
```

## Troubleshooting

### Common Issues

#### Widget Not Appearing
1. **Check Code Injection**: Ensure code is in Footer section, not Header
2. **Clear Cache**: Clear browser cache and Squarespace cache
3. **Check Venue ID**: Verify your venue ID is correct
4. **Test in Incognito**: Try viewing in private/incognito mode

#### Style Conflicts
1. **Z-Index Issues**: Add custom CSS to increase z-index
2. **Position Problems**: Ensure `position: fixed` in custom CSS
3. **Mobile Issues**: Test on actual mobile devices, not just browser resize

#### Template-Specific Problems
1. **7.0 vs 7.1**: Different templates may need different approaches
2. **Custom Templates**: May require additional CSS adjustments
3. **Third-Party Plugins**: Disable other chat/popup plugins temporarily

### Debug Mode
Enable debug logging to troubleshoot:

```html
<script src="https://cdn.evoque.digital/widget/v2/loader.js" 
        data-venue-id="YOUR_VENUE_ID"
        data-debug="true"
        async defer>
</script>
```

Then check browser console (F12) for detailed logs.

## Performance Optimization

### Loading Speed
- Widget loads asynchronously (won't slow down your site)
- Minimal impact on Squarespace performance
- CDN delivery ensures fast loading

### SEO Considerations
- No negative impact on SEO
- Loads after page content
- Doesn't affect Core Web Vitals
- Compatible with Squarespace SEO features

## Best Practices

### Design Integration
1. **Match Your Theme**: Use colors that complement your design
2. **Font Consistency**: Use fonts that match your site
3. **Position Carefully**: Choose position that doesn't interfere with navigation

### Content Strategy
1. **Personalized Messages**: Customize welcome messages for different pages
2. **Call-to-Action**: Use compelling language that encourages engagement
3. **Venue-Specific**: Mention your venue name and unique selling points

### Testing
1. **Multiple Devices**: Test on desktop, tablet, and mobile
2. **Different Browsers**: Check Chrome, Safari, Firefox, Edge
3. **Page Speed**: Monitor site speed after installation
4. **User Experience**: Have others test the widget functionality

## Example Implementations

### Elegant Wedding Venue
```html
<script src="https://cdn.evoque.digital/widget/v2/loader.js" 
        data-venue-id="elegant_gardens_123"
        data-primary-color="#d4af37"
        data-text-color="#ffffff"
        data-welcome-message="Welcome to Elegant Gardens! Ready to create magical wedding memories? Let's start planning!"
        data-font-family="'Playfair Display', serif"
        async defer>
</script>
```

### Modern Minimalist Venue
```html
<script src="https://cdn.evoque.digital/widget/v2/loader.js" 
        data-venue-id="modern_space_456"
        data-primary-color="#2c3e50"
        data-text-color="#ffffff"
        data-welcome-message="Hi! Looking for a contemporary wedding venue? We'd love to show you our space."
        data-font-family="'Helvetica Neue', Arial, sans-serif"
        async defer>
</script>
```

### Rustic Barn Venue
```html
<script src="https://cdn.evoque.digital/widget/v2/loader.js" 
        data-venue-id="rustic_barn_789"
        data-primary-color="#8b4513"
        data-text-color="#ffffff"
        data-welcome-message="Howdy! Dreaming of a rustic barn wedding? Let's chat about your country celebration!"
        data-font-family="'Georgia', serif"
        async defer>
</script>
```

## Support

### Getting Help
- **Squarespace Issues**: Check Squarespace Help Center
- **Widget Issues**: Contact <EMAIL>
- **Documentation**: Visit docs.evoque.digital

### Updates
- Widget updates automatically
- No manual updates required
- New features appear automatically

### Community
- Join our venue owner community
- Share best practices
- Get tips from other Squarespace users
