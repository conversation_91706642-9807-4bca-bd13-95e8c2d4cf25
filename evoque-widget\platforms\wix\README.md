# Evoque Widget - Wix Integration Guide

## Quick Installation (2 minutes)

### Method 1: HTML Element (Recommended)

1. **Open Wix Editor**
   - Go to your Wix website editor
   - Click on the page where you want to add the widget

2. **Add HTML Element**
   - Click the "+" button to add elements
   - Go to "Embed" → "HTML iframe"
   - Drag the HTML element to your page

3. **Add Widget Code**
   - Double-click the HTML element
   - Click "Enter Code"
   - Paste this code:

```html
<script src="https://cdn.evoque.digital/widget/v2/loader.js" 
        data-venue-id="YOUR_VENUE_ID"
        data-primary-color="#6366F1"
        data-welcome-message="Hello! How can I help you with your wedding venue inquiry?"
        data-fallback-mode="true"
        async defer>
</script>
```

4. **Configure Your Settings**
   - Replace `YOUR_VENUE_ID` with your actual venue ID
   - Customize colors and messages as needed
   - The `data-fallback-mode="true"` is important for Wix compatibility

5. **Position the Element**
   - You can place the HTML element anywhere on the page
   - The widget will automatically position itself correctly
   - We recommend placing it in the footer or a hidden area

6. **Publish Your Site**
   - Click "Publish" to make the widget live
   - Test the widget on your published site

### Method 2: Tracking & Analytics Code

1. **Go to Site Settings**
   - In the Wix Editor, click "Settings" in the top menu
   - Select "Tracking & Analytics"

2. **Add Custom Code**
   - Click "New Tool"
   - Choose "Custom" from the dropdown
   - Paste the widget code in the "Add Code Snippet" field

3. **Configure Placement**
   - Set "Place Code in" to "Body - end"
   - Choose "All Pages" or specific pages
   - Click "Apply"

## Wix-Specific Features

### Automatic Iframe Mode
Wix automatically uses our iframe fallback mode for security reasons. This means:
- ✅ Widget loads in a secure iframe
- ✅ No conflicts with Wix's security policies
- ✅ Responsive design works perfectly
- ✅ All features available

### Mobile Optimization
The widget is fully optimized for Wix mobile sites:
- Responsive design adapts to mobile screens
- Touch-friendly interface
- Fast loading on mobile devices
- Works with Wix's mobile editor

### Wix ADI Compatibility
If you're using Wix ADI (Artificial Design Intelligence):
1. Switch to Wix Editor mode
2. Follow the HTML Element method above
3. The widget will work seamlessly with ADI-generated designs

## Customization Options

### Colors and Branding
```html
<script src="https://cdn.evoque.digital/widget/v2/loader.js" 
        data-venue-id="YOUR_VENUE_ID"
        data-primary-color="#YOUR_BRAND_COLOR"
        data-text-color="#FFFFFF"
        data-position="bottom-right"
        data-show-branding="false"
        data-fallback-mode="true">
</script>
```

### Position Options
- `bottom-right` (default)
- `bottom-left`
- `top-right`
- `top-left`

### Welcome Message
Customize the first message visitors see:
```html
data-welcome-message="Welcome to [Your Venue Name]! How can we help make your wedding dreams come true?"
```

## Troubleshooting

### Widget Not Appearing
1. **Check Venue ID**: Make sure your venue ID is correct
2. **Clear Cache**: Clear your browser cache and refresh
3. **Check Console**: Open browser dev tools (F12) and look for errors
4. **Try Different Browser**: Test in Chrome, Firefox, and Safari

### Widget Appears But Doesn't Work
1. **Enable Debug Mode**: Add `data-debug="true"` to see detailed logs
2. **Check Internet Connection**: Ensure stable internet connection
3. **Disable Ad Blockers**: Some ad blockers may interfere

### Mobile Issues
1. **Test on Real Device**: Mobile emulators may not show true behavior
2. **Check Wix Mobile Editor**: Ensure the HTML element is enabled for mobile
3. **Adjust Position**: Try different position settings for mobile

### Styling Conflicts
If the widget doesn't look right:
1. **Use Iframe Mode**: Ensure `data-fallback-mode="true"` is set
2. **Check Z-Index**: The widget uses high z-index values
3. **Custom CSS**: Add custom CSS in Wix's custom CSS section if needed

## Advanced Configuration

### Custom CSS (Optional)
If you need to override widget styles, add this to your Wix custom CSS:

```css
/* Adjust widget position */
.evoque-widget-iframe {
    bottom: 20px !important;
    right: 20px !important;
}

/* Customize for mobile */
@media (max-width: 768px) {
    .evoque-widget-iframe {
        bottom: 10px !important;
        right: 10px !important;
    }
}
```

### Multiple Pages
To add the widget to multiple pages:
1. Use Method 2 (Tracking & Analytics) for site-wide installation
2. Or copy the HTML element to each page individually

### Event Tracking
Track widget interactions with Wix Analytics:

```html
<script>
// Listen for widget events
window.addEventListener('message', function(event) {
    if (event.data.type === 'evoque-widget-opened') {
        // Track with Wix Analytics
        wixWindow.trackEvent('Widget Opened');
    }
});
</script>
```

## Performance Optimization

### Loading Speed
- Widget loads asynchronously (won't slow down your site)
- Iframe mode reduces bundle size
- CDN delivery ensures fast loading worldwide

### SEO Impact
- Widget doesn't affect SEO rankings
- Loads after page content is ready
- No impact on Core Web Vitals

## Support and Updates

### Getting Help
- **Wix-Specific Issues**: Check Wix Help Center first
- **Widget Issues**: Contact <EMAIL>
- **Documentation**: Visit docs.evoque.digital

### Updates
- Widget updates automatically
- No need to update code manually
- New features appear automatically

### Compatibility
- ✅ Wix Editor
- ✅ Wix ADI
- ✅ Wix Mobile Editor
- ✅ All Wix templates
- ✅ Wix Stores
- ✅ Wix Bookings

## Best Practices

1. **Test Thoroughly**: Always test on desktop and mobile
2. **Monitor Performance**: Check site speed after installation
3. **Customize Messages**: Personalize welcome messages for your venue
4. **Track Analytics**: Monitor widget usage in your Evoque dashboard
5. **Keep Updated**: Check for new features and updates regularly

## Example Implementation

Here's a complete example for a wedding venue:

```html
<script src="https://cdn.evoque.digital/widget/v2/loader.js" 
        data-venue-id="venue_123456"
        data-primary-color="#d4af37"
        data-text-color="#ffffff"
        data-position="bottom-right"
        data-welcome-message="Welcome to Elegant Gardens! Ready to plan your dream wedding? Let's chat!"
        data-show-branding="true"
        data-fallback-mode="true"
        data-debug="false"
        async defer>
</script>
```

This configuration:
- Uses a gold color scheme (`#d4af37`)
- Positions widget in bottom-right corner
- Shows a personalized welcome message
- Enables Wix-compatible iframe mode
- Keeps Evoque branding for credibility
