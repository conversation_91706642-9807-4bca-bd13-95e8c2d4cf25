<?php
/**
 * Plugin Name: Evoque Wedding Venue Widget
 * Plugin URI: https://evoque.digital/widget
 * Description: Add the Evoque chat widget to your wedding venue website for instant lead capture and AI-powered responses.
 * Version: 1.0.0
 * Author: Evoque.Digital
 * Author URI: https://evoque.digital
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: evoque-widget
 * Domain Path: /languages
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('EVOQUE_WIDGET_VERSION', '1.0.0');
define('EVOQUE_WIDGET_PLUGIN_URL', plugin_dir_url(__FILE__));
define('EVOQUE_WIDGET_PLUGIN_PATH', plugin_dir_path(__FILE__));

class EvoqueWidget {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
    }
    
    public function init() {
        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Register settings
        add_action('admin_init', array($this, 'register_settings'));
        
        // Add widget to frontend
        add_action('wp_footer', array($this, 'add_widget_script'));
        
        // Add admin styles and scripts
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));
        
        // Add settings link to plugins page
        add_filter('plugin_action_links_' . plugin_basename(__FILE__), array($this, 'add_settings_link'));
    }
    
    public function add_admin_menu() {
        add_options_page(
            'Evoque Widget Settings',
            'Evoque Widget',
            'manage_options',
            'evoque-widget',
            array($this, 'admin_page')
        );
    }
    
    public function register_settings() {
        register_setting('evoque_widget_settings', 'evoque_widget_venue_id');
        register_setting('evoque_widget_settings', 'evoque_widget_primary_color');
        register_setting('evoque_widget_settings', 'evoque_widget_text_color');
        register_setting('evoque_widget_settings', 'evoque_widget_position');
        register_setting('evoque_widget_settings', 'evoque_widget_welcome_message');
        register_setting('evoque_widget_settings', 'evoque_widget_show_branding');
        register_setting('evoque_widget_settings', 'evoque_widget_enabled');
        register_setting('evoque_widget_settings', 'evoque_widget_debug');
    }
    
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>Evoque Widget Settings</h1>
            
            <?php if (isset($_GET['settings-updated'])): ?>
                <div class="notice notice-success is-dismissible">
                    <p>Settings saved successfully!</p>
                </div>
            <?php endif; ?>
            
            <form method="post" action="options.php">
                <?php settings_fields('evoque_widget_settings'); ?>
                <?php do_settings_sections('evoque_widget_settings'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">Enable Widget</th>
                        <td>
                            <input type="checkbox" name="evoque_widget_enabled" value="1" 
                                   <?php checked(1, get_option('evoque_widget_enabled', 1)); ?> />
                            <label>Enable the Evoque widget on your website</label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">Venue ID</th>
                        <td>
                            <input type="text" name="evoque_widget_venue_id" 
                                   value="<?php echo esc_attr(get_option('evoque_widget_venue_id')); ?>" 
                                   class="regular-text" required />
                            <p class="description">Your unique venue identifier from Evoque.Digital</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">Primary Color</th>
                        <td>
                            <input type="color" name="evoque_widget_primary_color" 
                                   value="<?php echo esc_attr(get_option('evoque_widget_primary_color', '#6366F1')); ?>" />
                            <p class="description">Widget button and accent color</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">Text Color</th>
                        <td>
                            <input type="color" name="evoque_widget_text_color" 
                                   value="<?php echo esc_attr(get_option('evoque_widget_text_color', '#FFFFFF')); ?>" />
                            <p class="description">Text color for buttons and headers</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">Position</th>
                        <td>
                            <select name="evoque_widget_position">
                                <option value="bottom-right" <?php selected(get_option('evoque_widget_position', 'bottom-right'), 'bottom-right'); ?>>Bottom Right</option>
                                <option value="bottom-left" <?php selected(get_option('evoque_widget_position'), 'bottom-left'); ?>>Bottom Left</option>
                                <option value="top-right" <?php selected(get_option('evoque_widget_position'), 'top-right'); ?>>Top Right</option>
                                <option value="top-left" <?php selected(get_option('evoque_widget_position'), 'top-left'); ?>>Top Left</option>
                            </select>
                            <p class="description">Where to position the widget on your pages</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">Welcome Message</th>
                        <td>
                            <textarea name="evoque_widget_welcome_message" rows="3" cols="50" class="large-text"><?php 
                                echo esc_textarea(get_option('evoque_widget_welcome_message', 'Hello! How can I help you with your wedding venue inquiry?')); 
                            ?></textarea>
                            <p class="description">First message visitors see when they open the chat</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">Show Branding</th>
                        <td>
                            <input type="checkbox" name="evoque_widget_show_branding" value="1" 
                                   <?php checked(1, get_option('evoque_widget_show_branding', 1)); ?> />
                            <label>Show "Powered by Evoque" branding</label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">Debug Mode</th>
                        <td>
                            <input type="checkbox" name="evoque_widget_debug" value="1" 
                                   <?php checked(1, get_option('evoque_widget_debug', 0)); ?> />
                            <label>Enable debug logging (for troubleshooting)</label>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button(); ?>
            </form>
            
            <div class="evoque-widget-info">
                <h2>Need Help?</h2>
                <p>
                    <strong>Documentation:</strong> <a href="https://docs.evoque.digital" target="_blank">docs.evoque.digital</a><br>
                    <strong>Support:</strong> <a href="mailto:<EMAIL>"><EMAIL></a><br>
                    <strong>Website:</strong> <a href="https://evoque.digital" target="_blank">evoque.digital</a>
                </p>
                
                <h3>Widget Preview</h3>
                <div id="evoque-widget-preview" style="border: 1px solid #ddd; padding: 20px; background: #f9f9f9; margin-top: 10px;">
                    <p>Save your settings to see a preview of your widget here.</p>
                </div>
            </div>
        </div>
        
        <style>
            .evoque-widget-info {
                margin-top: 30px;
                padding: 20px;
                background: #fff;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            
            .evoque-widget-info h2 {
                margin-top: 0;
            }
            
            #evoque-widget-preview {
                min-height: 100px;
                position: relative;
            }
        </style>
        <?php
    }
    
    public function add_widget_script() {
        // Only add if enabled and venue ID is set
        if (!get_option('evoque_widget_enabled', 1) || !get_option('evoque_widget_venue_id')) {
            return;
        }
        
        $venue_id = esc_attr(get_option('evoque_widget_venue_id'));
        $primary_color = esc_attr(get_option('evoque_widget_primary_color', '#6366F1'));
        $text_color = esc_attr(get_option('evoque_widget_text_color', '#FFFFFF'));
        $position = esc_attr(get_option('evoque_widget_position', 'bottom-right'));
        $welcome_message = esc_attr(get_option('evoque_widget_welcome_message', 'Hello! How can I help you with your wedding venue inquiry?'));
        $show_branding = get_option('evoque_widget_show_branding', 1) ? 'true' : 'false';
        $debug = get_option('evoque_widget_debug', 0) ? 'true' : 'false';
        
        ?>
        <script src="https://cdn.evoque.digital/widget/v2/loader.js" 
                data-venue-id="<?php echo $venue_id; ?>"
                data-primary-color="<?php echo $primary_color; ?>"
                data-text-color="<?php echo $text_color; ?>"
                data-position="<?php echo $position; ?>"
                data-welcome-message="<?php echo $welcome_message; ?>"
                data-show-branding="<?php echo $show_branding; ?>"
                data-debug="<?php echo $debug; ?>"
                async defer>
        </script>
        <?php
    }
    
    public function admin_enqueue_scripts($hook) {
        if ($hook !== 'settings_page_evoque-widget') {
            return;
        }
        
        wp_enqueue_script('wp-color-picker');
        wp_enqueue_style('wp-color-picker');
        
        wp_add_inline_script('wp-color-picker', '
            jQuery(document).ready(function($) {
                $("input[type=color]").wpColorPicker();
            });
        ');
    }
    
    public function add_settings_link($links) {
        $settings_link = '<a href="options-general.php?page=evoque-widget">Settings</a>';
        array_unshift($links, $settings_link);
        return $links;
    }
}

// Initialize the plugin
new EvoqueWidget();

// Activation hook
register_activation_hook(__FILE__, function() {
    // Set default options
    add_option('evoque_widget_enabled', 1);
    add_option('evoque_widget_primary_color', '#6366F1');
    add_option('evoque_widget_text_color', '#FFFFFF');
    add_option('evoque_widget_position', 'bottom-right');
    add_option('evoque_widget_welcome_message', 'Hello! How can I help you with your wedding venue inquiry?');
    add_option('evoque_widget_show_branding', 1);
    add_option('evoque_widget_debug', 0);
});

// Deactivation hook
register_deactivation_hook(__FILE__, function() {
    // Clean up if needed
});

// Uninstall hook
register_uninstall_hook(__FILE__, function() {
    // Remove options
    delete_option('evoque_widget_venue_id');
    delete_option('evoque_widget_enabled');
    delete_option('evoque_widget_primary_color');
    delete_option('evoque_widget_text_color');
    delete_option('evoque_widget_position');
    delete_option('evoque_widget_welcome_message');
    delete_option('evoque_widget_show_branding');
    delete_option('evoque_widget_debug');
});
?>
