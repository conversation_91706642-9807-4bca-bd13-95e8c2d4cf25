<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Evoque Wedding Venue Chat</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: transparent;
            overflow: hidden;
        }

        .widget-container {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chat-button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--primary-color, #6366F1);
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            color: var(--text-color, #FFFFFF);
        }

        .chat-button:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }

        .chat-button svg {
            width: 24px;
            height: 24px;
            fill: currentColor;
        }

        .chat-interface {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 350px;
            height: 500px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            display: none;
            flex-direction: column;
            overflow: hidden;
            z-index: 1000;
        }

        .chat-interface.open {
            display: flex;
        }

        .chat-header {
            background: var(--primary-color, #6366F1);
            color: var(--text-color, #FFFFFF);
            padding: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-header h3 {
            font-size: 16px;
            font-weight: 600;
        }

        .close-button {
            background: none;
            border: none;
            color: currentColor;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
        }

        .close-button:hover {
            background: rgba(255,255,255,0.1);
        }

        .chat-messages {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
            background: #f9fafb;
        }

        .message {
            margin-bottom: 12px;
            padding: 12px;
            border-radius: 8px;
            max-width: 80%;
        }

        .message.bot {
            background: white;
            border: 1px solid #e5e7eb;
            margin-right: auto;
        }

        .message.user {
            background: var(--primary-color, #6366F1);
            color: var(--text-color, #FFFFFF);
            margin-left: auto;
        }

        .chat-input {
            padding: 16px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            gap: 8px;
        }

        .chat-input input {
            flex: 1;
            padding: 12px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 14px;
        }

        .chat-input input:focus {
            outline: none;
            border-color: var(--primary-color, #6366F1);
        }

        .send-button {
            padding: 12px 16px;
            background: var(--primary-color, #6366F1);
            color: var(--text-color, #FFFFFF);
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
        }

        .send-button:hover {
            opacity: 0.9;
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @media (max-width: 768px) {
            .chat-interface {
                width: calc(100vw - 40px);
                height: calc(100vh - 100px);
                right: 20px;
                bottom: 80px;
            }
        }
    </style>
</head>
<body>
    <div class="widget-container">
        <button class="chat-button" id="chatButton" aria-label="Open chat">
            <svg viewBox="0 0 24 24">
                <path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H6l-2 2V4h16v12z"/>
            </svg>
        </button>
    </div>

    <div class="chat-interface" id="chatInterface">
        <div class="chat-header">
            <h3>Wedding Venue Chat</h3>
            <button class="close-button" id="closeButton" aria-label="Close chat">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
            </button>
        </div>
        <div class="chat-messages" id="chatMessages">
            <div class="message bot" id="welcomeMessage">
                Hello! How can I help you with your wedding venue inquiry?
            </div>
        </div>
        <div class="chat-input">
            <input type="text" id="messageInput" placeholder="Type your message..." maxlength="500">
            <button class="send-button" id="sendButton">Send</button>
        </div>
    </div>

    <script>
        // Parse URL parameters for configuration
        const urlParams = new URLSearchParams(window.location.search);
        const config = {
            venueId: urlParams.get('venueId') || '',
            primaryColor: urlParams.get('primaryColor') || '#6366F1',
            textColor: urlParams.get('textColor') || '#FFFFFF',
            welcomeMessage: urlParams.get('welcomeMessage') || 'Hello! How can I help you with your wedding venue inquiry?',
            socketUrl: urlParams.get('socketUrl') || 'https://api.evoque.digital'
        };

        // Apply configuration
        document.documentElement.style.setProperty('--primary-color', config.primaryColor);
        document.documentElement.style.setProperty('--text-color', config.textColor);
        document.getElementById('welcomeMessage').textContent = config.welcomeMessage;

        // Widget functionality
        const chatButton = document.getElementById('chatButton');
        const chatInterface = document.getElementById('chatInterface');
        const closeButton = document.getElementById('closeButton');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const chatMessages = document.getElementById('chatMessages');

        let isOpen = false;

        function toggleChat() {
            isOpen = !isOpen;
            chatInterface.classList.toggle('open', isOpen);
            
            if (isOpen) {
                messageInput.focus();
                // Notify parent window
                window.parent.postMessage({ type: 'evoque-widget-opened' }, '*');
            } else {
                window.parent.postMessage({ type: 'evoque-widget-closed' }, '*');
            }
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // Add user message
            addMessage(message, 'user');
            messageInput.value = '';
            sendButton.disabled = true;

            // Send to API (simplified for iframe)
            fetch(`${config.socketUrl}/api/chat/message`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    venueId: config.venueId,
                    message: message,
                    source: 'iframe-widget'
                })
            })
            .then(response => response.json())
            .then(data => {
                addMessage(data.response || 'Thank you for your message. We\'ll get back to you soon!', 'bot');
            })
            .catch(error => {
                addMessage('Sorry, there was an error sending your message. Please try again.', 'bot');
            })
            .finally(() => {
                sendButton.disabled = false;
            });
        }

        function addMessage(text, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = text;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Event listeners
        chatButton.addEventListener('click', toggleChat);
        closeButton.addEventListener('click', toggleChat);
        sendButton.addEventListener('click', sendMessage);
        
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        messageInput.addEventListener('input', () => {
            sendButton.disabled = !messageInput.value.trim();
        });

        // Listen for messages from parent window
        window.addEventListener('message', (event) => {
            if (event.data.type === 'evoque-widget-open') {
                if (!isOpen) toggleChat();
            } else if (event.data.type === 'evoque-widget-close') {
                if (isOpen) toggleChat();
            }
        });

        // Resize iframe based on state
        function updateIframeSize() {
            const size = isOpen ? { width: 370, height: 520 } : { width: 60, height: 60 };
            window.parent.postMessage({ 
                type: 'evoque-widget-resize', 
                size: size 
            }, '*');
        }

        // Update size when chat state changes
        const observer = new MutationObserver(() => {
            updateIframeSize();
        });
        observer.observe(chatInterface, { attributes: true, attributeFilter: ['class'] });

        // Initial setup
        sendButton.disabled = true;
        
        // Track widget load
        fetch(`${config.socketUrl}/api/analytics/widget-load`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                venueId: config.venueId,
                type: 'iframe',
                domain: document.referrer ? new URL(document.referrer).hostname : 'unknown'
            })
        }).catch(() => {}); // Fail silently
    </script>
</body>
</html>
