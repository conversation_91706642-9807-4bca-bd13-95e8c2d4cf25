/**
 * Universal Widget Loader Script
 * 
 * This script provides one-click installation for the Evoque widget across all platforms.
 * Features:
 * - Auto-configuration from data attributes or domain detection
 * - Platform detection and optimization
 * - Error handling and fallback modes
 * - Progressive loading for better performance
 */

interface LoaderConfig {
  venueId?: string;
  primaryColor?: string;
  textColor?: string;
  position?: string;
  welcomeMessage?: string;
  fontFamily?: string;
  fontSize?: string;
  buttonIcon?: string;
  showBranding?: boolean;
  mobileBreakpoint?: number;
  socketUrl?: string;
  autoDetect?: boolean;
  fallbackMode?: boolean;
  debug?: boolean;
}

interface PlatformInfo {
  name: string;
  detected: boolean;
  optimizations: string[];
}

class EvoqueWidgetLoader {
  private config: LoaderConfig;
  private platform: PlatformInfo;
  private widgetInstance: any = null;
  private loadAttempts = 0;
  private maxLoadAttempts = 3;
  private cdnUrl = 'https://cdn.evoque.digital/widget/v2';
  private apiUrl = 'https://api.evoque.digital';

  constructor(config: LoaderConfig = {}) {
    this.config = this.mergeWithDefaults(config);
    this.platform = this.detectPlatform();
    this.init();
  }

  /**
   * Initialize the widget loader
   */
  private async init(): Promise<void> {
    try {
      // Wait for DOM to be ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => this.load());
      } else {
        await this.load();
      }
    } catch (error) {
      this.handleError('Initialization failed', error);
    }
  }

  /**
   * Load the widget with progressive enhancement
   */
  private async load(): Promise<void> {
    try {
      this.loadAttempts++;
      
      // Auto-detect venue if enabled
      if (this.config.autoDetect && !this.config.venueId) {
        await this.autoDetectVenue();
      }

      // Validate configuration
      if (!this.config.venueId) {
        throw new Error('Venue ID is required');
      }

      // Apply platform-specific optimizations
      this.applyPlatformOptimizations();

      // Load widget script
      await this.loadWidgetScript();

      // Initialize widget
      this.initializeWidget();

      // Track successful installation
      this.trackInstallation('success');

    } catch (error) {
      this.handleLoadError(error);
    }
  }

  /**
   * Merge configuration with defaults
   */
  private mergeWithDefaults(config: LoaderConfig): LoaderConfig {
    const scriptTag = document.querySelector('script[data-venue-id]') as HTMLScriptElement;
    
    return {
      venueId: config.venueId || scriptTag?.dataset.venueId || '',
      primaryColor: config.primaryColor || scriptTag?.dataset.primaryColor || '#6366F1',
      textColor: config.textColor || scriptTag?.dataset.textColor || '#FFFFFF',
      position: config.position || scriptTag?.dataset.position || 'bottom-right',
      welcomeMessage: config.welcomeMessage || scriptTag?.dataset.welcomeMessage || 'Hello! How can I help you with your wedding venue inquiry?',
      fontFamily: config.fontFamily || scriptTag?.dataset.fontFamily || 'Arial, sans-serif',
      fontSize: config.fontSize || scriptTag?.dataset.fontSize || '16px',
      buttonIcon: config.buttonIcon || scriptTag?.dataset.buttonIcon || 'chat',
      showBranding: config.showBranding !== undefined ? config.showBranding : (scriptTag?.dataset.showBranding !== 'false'),
      mobileBreakpoint: config.mobileBreakpoint || parseInt(scriptTag?.dataset.mobileBreakpoint || '768'),
      socketUrl: config.socketUrl || scriptTag?.dataset.socketUrl || this.apiUrl,
      autoDetect: config.autoDetect !== undefined ? config.autoDetect : (scriptTag?.dataset.autoDetect !== 'false'),
      fallbackMode: config.fallbackMode !== undefined ? config.fallbackMode : (scriptTag?.dataset.fallbackMode === 'true'),
      debug: config.debug !== undefined ? config.debug : (scriptTag?.dataset.debug === 'true'),
    };
  }

  /**
   * Detect the platform and its characteristics
   */
  private detectPlatform(): PlatformInfo {
    const userAgent = navigator.userAgent.toLowerCase();
    const hostname = window.location.hostname.toLowerCase();
    const bodyClasses = document.body.className.toLowerCase();
    const metaTags = Array.from(document.querySelectorAll('meta[name="generator"]'))
      .map(meta => (meta as HTMLMetaElement).content.toLowerCase());

    // WordPress detection
    if (bodyClasses.includes('wordpress') || 
        metaTags.some(content => content.includes('wordpress')) ||
        document.querySelector('link[href*="wp-content"]')) {
      return {
        name: 'wordpress',
        detected: true,
        optimizations: ['shadow-dom', 'namespace-isolation', 'wp-specific-css']
      };
    }

    // Wix detection
    if (hostname.includes('wixsite.com') || 
        hostname.includes('wix.com') ||
        document.querySelector('meta[name="generator"][content*="Wix"]')) {
      return {
        name: 'wix',
        detected: true,
        optimizations: ['iframe-fallback', 'wix-specific-positioning', 'reduced-bundle']
      };
    }

    // Squarespace detection
    if (bodyClasses.includes('squarespace') ||
        metaTags.some(content => content.includes('squarespace'))) {
      return {
        name: 'squarespace',
        detected: true,
        optimizations: ['squarespace-css-reset', 'position-adjustment']
      };
    }

    // Shopify detection
    if (window.Shopify || 
        document.querySelector('script[src*="shopify"]') ||
        metaTags.some(content => content.includes('shopify'))) {
      return {
        name: 'shopify',
        detected: true,
        optimizations: ['shopify-theme-compat', 'cart-integration']
      };
    }

    // Webflow detection
    if (bodyClasses.includes('w-') || 
        document.querySelector('script[src*="webflow"]')) {
      return {
        name: 'webflow',
        detected: true,
        optimizations: ['webflow-interactions', 'custom-positioning']
      };
    }

    return {
      name: 'generic',
      detected: false,
      optimizations: ['standard-positioning', 'cross-browser-compat']
    };
  }

  /**
   * Auto-detect venue from domain or URL patterns
   */
  private async autoDetectVenue(): Promise<void> {
    try {
      const domain = window.location.hostname;
      const response = await fetch(`${this.apiUrl}/api/venues/detect?domain=${encodeURIComponent(domain)}`);
      
      if (response.ok) {
        const data = await response.json();
        if (data.venueId) {
          this.config.venueId = data.venueId;
          this.log('Auto-detected venue:', data.venueId);
        }
      }
    } catch (error) {
      this.log('Auto-detection failed, manual venue ID required');
    }
  }

  /**
   * Apply platform-specific optimizations
   */
  private applyPlatformOptimizations(): void {
    const optimizations = this.platform.optimizations;

    // Shadow DOM for style isolation
    if (optimizations.includes('shadow-dom')) {
      this.config = { ...this.config, useShadowDOM: true } as any;
    }

    // Iframe fallback for restrictive platforms
    if (optimizations.includes('iframe-fallback') && this.config.fallbackMode) {
      this.loadIframeFallback();
      return;
    }

    // Platform-specific CSS adjustments
    if (optimizations.includes('wix-specific-positioning')) {
      this.injectPlatformCSS('.evoque-widget { z-index: 999999 !important; }');
    }

    if (optimizations.includes('squarespace-css-reset')) {
      this.injectPlatformCSS('.evoque-widget * { box-sizing: border-box !important; }');
    }

    this.log(`Applied optimizations for ${this.platform.name}:`, optimizations);
  }

  /**
   * Load the main widget script
   */
  private async loadWidgetScript(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Check if widget is already loaded
      if (window.EvoqueWidget) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = `${this.cdnUrl}/evoque-widget.js`;
      script.async = true;
      script.defer = true;

      script.onload = () => {
        this.log('Widget script loaded successfully');
        resolve();
      };

      script.onerror = () => {
        reject(new Error('Failed to load widget script'));
      };

      // Add integrity check for security
      script.integrity = 'sha384-...'; // This would be the actual hash
      script.crossOrigin = 'anonymous';

      document.head.appendChild(script);
    });
  }

  /**
   * Initialize the widget instance
   */
  private initializeWidget(): void {
    if (!window.EvoqueWidget) {
      throw new Error('Widget class not available');
    }

    this.widgetInstance = new window.EvoqueWidget(this.config);
    this.log('Widget initialized successfully');

    // Set up error handling
    window.addEventListener('error', (event) => {
      if (event.filename?.includes('evoque-widget')) {
        this.handleError('Widget runtime error', event.error);
      }
    });
  }

  /**
   * Load iframe fallback for restrictive environments
   */
  private loadIframeFallback(): void {
    const iframe = document.createElement('iframe');
    iframe.src = `${this.cdnUrl}/iframe.html?${new URLSearchParams(this.config as any).toString()}`;
    iframe.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 60px;
      height: 60px;
      border: none;
      border-radius: 50%;
      z-index: 999999;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    iframe.title = 'Evoque Wedding Venue Chat';

    document.body.appendChild(iframe);
    this.log('Iframe fallback loaded');
  }

  /**
   * Inject platform-specific CSS
   */
  private injectPlatformCSS(css: string): void {
    const style = document.createElement('style');
    style.textContent = css;
    document.head.appendChild(style);
  }

  /**
   * Handle load errors with retry logic
   */
  private handleLoadError(error: any): void {
    this.log('Load error:', error);

    if (this.loadAttempts < this.maxLoadAttempts) {
      this.log(`Retrying... (${this.loadAttempts}/${this.maxLoadAttempts})`);
      setTimeout(() => this.load(), 2000 * this.loadAttempts);
    } else if (!this.config.fallbackMode) {
      this.log('Max retries reached, trying fallback mode');
      this.config.fallbackMode = true;
      this.loadIframeFallback();
    } else {
      this.handleError('Widget failed to load after all attempts', error);
    }
  }

  /**
   * Handle errors and provide user feedback
   */
  private handleError(message: string, error?: any): void {
    console.error(`[Evoque Widget] ${message}:`, error);
    
    // Track error for debugging
    this.trackInstallation('error', { message, error: error?.message });

    // Show user-friendly error in debug mode
    if (this.config.debug) {
      const errorDiv = document.createElement('div');
      errorDiv.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: #ff4444;
        color: white;
        padding: 10px;
        border-radius: 5px;
        font-family: Arial, sans-serif;
        font-size: 12px;
        z-index: 999999;
      `;
      errorDiv.textContent = `Widget Error: ${message}`;
      document.body.appendChild(errorDiv);

      setTimeout(() => errorDiv.remove(), 5000);
    }
  }

  /**
   * Track installation analytics
   */
  private trackInstallation(status: 'success' | 'error', data?: any): void {
    try {
      fetch(`${this.apiUrl}/api/analytics/widget-installation`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          venueId: this.config.venueId,
          domain: window.location.hostname,
          platform: this.platform.name,
          status,
          data,
          timestamp: new Date().toISOString(),
        }),
      }).catch(() => {}); // Fail silently
    } catch (error) {
      // Fail silently for analytics
    }
  }

  /**
   * Debug logging
   */
  private log(...args: any[]): void {
    if (this.config.debug) {
      console.log('[Evoque Widget Loader]', ...args);
    }
  }

  /**
   * Public API methods
   */
  public getWidget() {
    return this.widgetInstance;
  }

  public getPlatform() {
    return this.platform;
  }

  public getConfig() {
    return this.config;
  }
}

// Auto-initialize if script tag is present
if (document.querySelector('script[data-venue-id]')) {
  new EvoqueWidgetLoader();
}

// Make loader available globally
declare global {
  interface Window {
    EvoqueWidgetLoader: typeof EvoqueWidgetLoader;
    EvoqueWidget: any;
  }
}

window.EvoqueWidgetLoader = EvoqueWidgetLoader;

export default EvoqueWidgetLoader;
