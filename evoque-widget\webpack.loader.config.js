const path = require('path');

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production';
  
  return {
    entry: './src/loader.ts',
    output: {
      path: path.resolve(__dirname, 'build'),
      filename: 'loader.js',
      library: 'EvoqueWidgetLoader',
      libraryTarget: 'umd',
      libraryExport: 'default',
      umdNamedDefine: true,
      publicPath: '/',
      clean: false, // Don't clean since we have multiple builds
    },
    module: {
      rules: [
        {
          test: /\.(ts|tsx)$/,
          exclude: /node_modules/,
          use: 'ts-loader',
        },
      ],
    },
    resolve: {
      extensions: ['.tsx', '.ts', '.js'],
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
    plugins: [],
    externals: {}, // No externals for the loader - it should be self-contained
    optimization: {
      minimize: isProduction,
    },
    devtool: isProduction ? 'source-map' : 'inline-source-map',
  };
};
